{"bsonType": "object", "required": ["status"], "properties": {"_id": {"description": "任务ID，系统自动生成"}, "taskId": {"bsonType": "string", "description": "自定义任务ID，用于前端追踪", "title": "任务ID"}, "type": {"bsonType": "string", "enum": ["upload", "link_parse"], "description": "任务类型：upload-本地上传，link_parse-链接解析", "title": "任务类型", "defaultValue": "upload"}, "platform": {"bsonType": "string", "enum": ["local", "do<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "description": "来源平台：local-本地上传，douyin-抖音，xiaohongshu-小红书", "title": "来源平台", "defaultValue": "local"}, "originalUrl": {"bsonType": "string", "description": "原始分享链接（仅链接解析类型）", "title": "原始链接"}, "videoTitle": {"bsonType": "string", "description": "视频标题", "title": "视频标题"}, "ossUrl": {"bsonType": "string", "description": "视频文件在阿里云OSS中的完整URL地址", "title": "视频OSS地址"}, "fileName": {"bsonType": "string", "description": "原始文件名", "title": "文件名"}, "fileSize": {"bsonType": "number", "description": "文件大小，单位为字节", "title": "文件大小"}, "estimatedSize": {"bsonType": "number", "description": "预估文件大小（链接解析时）", "title": "预估文件大小"}, "duration": {"bsonType": "number", "description": "视频时长，单位为秒", "title": "视频时长"}, "videoWidth": {"bsonType": "number", "description": "视频宽度，单位为像素", "title": "视频宽度"}, "videoHeight": {"bsonType": "number", "description": "视频高度，单位为像素", "title": "视频高度"}, "status": {"bsonType": "string", "enum": ["uploading", "downloading", "extracting_audio", "recognizing", "speech_recognition", "translating", "merging", "video_synthesis", "completed", "failed", "cancelled", "deleted"], "description": "任务状态", "title": "任务状态", "defaultValue": "uploading"}, "progress": {"bsonType": "object", "description": "详细进度信息", "title": "进度信息", "properties": {"downloading": {"bsonType": "object", "description": "下载进度（链接解析）", "properties": {"status": {"bsonType": "string"}, "progress": {"bsonType": "number"}, "startTime": {"bsonType": "timestamp"}, "ossUrl": {"bsonType": "string"}, "message": {"bsonType": "string"}}}, "extracting_audio": {"bsonType": "object", "description": "音频提取进度", "properties": {"status": {"bsonType": "string"}, "startTime": {"bsonType": "timestamp"}, "endTime": {"bsonType": "timestamp"}, "message": {"bsonType": "string"}}}, "speech_recognition": {"bsonType": "object", "description": "语音识别进度", "properties": {"status": {"bsonType": "string"}, "startTime": {"bsonType": "timestamp"}, "endTime": {"bsonType": "timestamp"}, "message": {"bsonType": "string"}}}, "translating": {"bsonType": "object", "description": "翻译进度", "properties": {"status": {"bsonType": "string"}, "startTime": {"bsonType": "timestamp"}, "endTime": {"bsonType": "timestamp"}, "message": {"bsonType": "string"}}}, "video_synthesis": {"bsonType": "object", "description": "视频合成进度", "properties": {"status": {"bsonType": "string"}, "startTime": {"bsonType": "timestamp"}, "endTime": {"bsonType": "timestamp"}, "message": {"bsonType": "string"}}}}}, "metadata": {"bsonType": "object", "description": "元数据信息", "title": "元数据", "properties": {"fromPlatform": {"bsonType": "string"}, "originalVideoUrl": {"bsonType": "string"}, "estimatedDuration": {"bsonType": "number"}, "estimatedFileSize": {"bsonType": "number"}, "thumbnailUrl": {"bsonType": "string"}}}, "audioOssUrl": {"bsonType": "string", "description": "提取的音频文件OSS地址", "title": "音频文件地址"}, "subtitleOssUrl": {"bsonType": "string", "description": "生成的字幕文件OSS地址", "title": "字幕文件地址"}, "finalVideoUrl": {"bsonType": "string", "description": "最终烧录字幕后的视频OSS地址", "title": "最终视频地址"}, "errorMessage": {"bsonType": "string", "description": "错误信息", "title": "错误信息"}, "audioExtractionJobId": {"bsonType": "string", "description": "音频提取MPS任务ID", "title": "音频提取任务ID"}, "subtitleMergeJobId": {"bsonType": "string", "description": "字幕烧录MPS任务ID", "title": "字幕烧录任务ID"}, "paraformerTaskId": {"bsonType": "string", "description": "阿里云Paraformer语音识别任务ID", "title": "Paraformer任务ID"}, "sourceLanguage": {"bsonType": "string", "description": "用户选择的源语言，auto表示自动识别", "title": "源语言", "defaultValue": "auto"}, "detectedLanguage": {"bsonType": "string", "description": "检测到的音频语言", "title": "检测语言"}, "targetLanguage": {"bsonType": "string", "description": "目标翻译语言，默认为zh（中文）", "title": "目标语言", "defaultValue": "zh"}, "userId": {"bsonType": "string", "description": "用户ID，用于权限控制", "title": "用户ID"}, "retryCount": {"bsonType": "number", "description": "重试次数", "title": "重试次数", "defaultValue": 0}, "isParsedVideo": {"bsonType": "boolean", "description": "是否为解析的视频（链接解析获得的视频）", "title": "是否为解析视频", "defaultValue": false}, "retryAt": {"bsonType": "timestamp", "description": "最后重试时间", "title": "重试时间"}, "deletedAt": {"bsonType": "timestamp", "description": "删除时间", "title": "删除时间"}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}, "createTime": {"bsonType": "timestamp", "description": "创建时间（兼容旧字段）", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updateTime": {"bsonType": "timestamp", "description": "更新时间（兼容旧字段）", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}, "backgroundMode": {"bsonType": "boolean", "description": "是否处于后台处理模式", "title": "后台模式", "defaultValue": false}, "translationStarted": {"bsonType": "boolean", "description": "翻译是否已经启动（用于避免重复启动翻译）", "title": "翻译已启动", "defaultValue": false}}}