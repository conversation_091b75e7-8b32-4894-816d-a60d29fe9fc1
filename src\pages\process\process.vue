<template>
  <view class="process-container">
    <!-- 现代化头部区域 -->
    <view class="hero-section">
      <view class="hero-header">
        <view class="status-indicator" :class="{ 
          'processing': !isCompleted && !isCancelling,
          'completed': isCompleted,
          'cancelling': isCancelling 
        }">
          <view class="pulse-ring" v-if="!isCompleted && !isCancelling"></view>
          <view class="status-icon">
            <text v-if="isCompleted">✅</text>
            <text v-else-if="isCancelling">⏸️</text>
            <text v-else>⚡</text>
          </view>
        </view>
        <view class="hero-text">
          <text class="hero-title">{{ statusText }}</text>
          <text class="hero-progress">{{ progress }}% 完成</text>
        </view>
      </view>
      
      <!-- 现代化进度条 -->
      <view class="modern-progress-container">
        <view class="progress-track">
          <view class="progress-fill-modern" :style="{ width: progress + '%' }">
            <view class="progress-shine"></view>
          </view>
        </view>
        <text class="progress-percentage">{{ progress }}%</text>
      </view>
    </view>

    <!-- 流程步骤卡片 -->
    <view class="steps-card">
      <view class="card-header-modern">
        <text class="card-title-modern">处理流程</text>
        <text class="card-subtitle">AI智能处理进行中</text>
      </view>
      
      <view class="steps-container">
        <view class="step-modern" 
              v-for="(step, index) in processSteps" 
              :key="index"
              :class="{ 
                'active': currentStep >= index + 1, 
                'completed': currentStep > index + 1,
                'current': currentStep === index + 1
              }">
          <view class="step-indicator-modern">
            <view class="step-circle">
              <text class="step-icon-modern">{{ step.icon }}</text>
              <view class="step-ripple" v-if="currentStep === index + 1"></view>
            </view>
            <view class="step-line" v-if="index < processSteps.length - 1"></view>
          </view>
          <view class="step-content-modern">
            <text class="step-title-modern">{{ step.title }}</text>
            <text class="step-desc-modern">{{ step.desc }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频信息卡片 - 现代化设计 -->
    <view class="info-card-modern">
      <view class="card-header-modern">
        <text class="card-title-modern">视频详情</text>
        <view class="info-badge">
          <text class="badge-text">{{ videoInfo.fileName ? '已识别' : '读取中' }}</text>
        </view>
      </view>
      <view class="info-grid">
        <view class="info-item-modern">
          <view class="info-icon-wrapper">
            <text class="info-icon">📁</text>
          </view>
          <view class="info-content">
            <text class="info-label-modern">文件名</text>
            <text class="info-value-modern">{{ videoInfo.fileName || '加载中...' }}</text>
          </view>
        </view>
        <view class="info-item-modern">
          <view class="info-icon-wrapper">
            <text class="info-icon">💾</text>
          </view>
          <view class="info-content">
            <text class="info-label-modern">文件大小</text>
            <text class="info-value-modern">{{ formatFileSize(videoInfo.fileSize) }}</text>
          </view>
        </view>
        <view v-if="videoInfo.duration && videoInfo.duration > 0 && Number.isFinite(videoInfo.duration)" class="info-item-modern">
          <view class="info-icon-wrapper">
            <text class="info-icon">⏱️</text>
          </view>
          <view class="info-content">
            <text class="info-label-modern">视频时长</text>
            <text class="info-value-modern">{{ formatDuration(videoInfo.duration) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 现代化操作区域 -->
    <view class="action-section-modern">
      <button v-if="isCompleted" @click="viewResult" class="btn-modern btn-primary-modern">
        <text class="btn-icon-modern">🎉</text>
        <text class="btn-text-modern">查看结果</text>
      </button>
      <button v-else-if="isCancelling" class="btn-modern btn-disabled-modern" disabled>
        <text class="btn-icon-modern">⏳</text>
        <text class="btn-text-modern">正在取消...</text>
      </button>
      <button v-else @click="cancelProcess" class="btn-modern btn-secondary-modern">
        <text class="btn-icon-modern">❌</text>
        <text class="btn-text-modern">取消处理</text>
      </button>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { onShow, onHide } from '@dcloudio/uni-app'
import { formatFileSize, formatDuration } from '@/utils/common'

// 响应式数据
const progress = ref(0)
const currentStep = ref(2)
const statusText = ref('正在处理中...')
const isCompleted = ref(false)
const taskId = ref('')
const isCancelling = ref(false)

// 处理步骤数据
const processSteps = ref([
  {
    icon: '📤',
    title: '上传视频',
    desc: '将视频文件上传到云端'
  },
  {
    icon: '🎤',
    title: '语音识别',
    desc: 'AI智能识别音频内容'
  },
  {
    icon: '📝',
    title: '生成字幕',
    desc: '智能翻译并生成字幕'
  },
  {
    icon: '🎬',
    title: '合成视频',
    desc: '将字幕合成到视频中'
  }
])

// 视频信息
const videoInfo = ref({
  fileName: '',
  fileSize: 0,
  duration: 0
})

// 定时器管理 - 统一管理
let pollingTimer: number | null = null
const isPageVisible = ref(true)
const isPolling = ref(false)
const isCheckingStatus = ref(false)

onMounted(() => {
  // 初始化进度状态
  initializeProgress()

  // 确保任务ID已设置
  const currentTaskId = ensureTaskId()

  if (currentTaskId) {
    // 开始查询任务状态
    startTaskStatusPolling()
  } else {
    // 显示错误提示
    uni.showModal({
      title: '参数错误',
      content: '缺少任务ID参数，请重新上传视频',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  }
})

// 页面显示监听器
onShow(() => {
  isPageVisible.value = true

  // 确保任务ID已设置
  const currentTaskId = ensureTaskId()

  if (currentTaskId) {
    // 恢复前台模式
    resumeFromBackgroundMode()

    // 如果任务未完成且未取消，恢复轮询
    if (!isCompleted.value && !isCancelling.value) {
      startPolling()
    }
  } else {
    // 延迟重试，等待onMounted完成
    setTimeout(() => {
      const retryTaskId = ensureTaskId()
      if (retryTaskId && isPageVisible.value) {
        resumeFromBackgroundMode()
        if (!isCompleted.value && !isCancelling.value) {
          startPolling()
        }
      }
    }, 1000)
  }
})

// 页面隐藏监听器
onHide(() => {
  const currentTaskId = taskId.value || getTaskIdFromPage()

  isPageVisible.value = false
  stopPolling()

  // 切换到后台模式
  if (currentTaskId && !isCompleted.value && !isCancelling.value) {
    switchToBackgroundMode()
  }
})

// 获取任务ID的辅助函数
const getTaskIdFromPage = (): string | null => {
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1] as any
    const options = currentPage.options || {}
    return options.taskId || null
  } catch (error) {
    return null
  }
}

// 确保任务ID已设置的函数
const ensureTaskId = (): string | null => {
  if (taskId.value) {
    return taskId.value
  }

  // 尝试从页面参数获取
  const pageTaskId = getTaskIdFromPage()
  if (pageTaskId) {
    taskId.value = pageTaskId
    return pageTaskId
  }

  return null
}


// 切换到后台模式
const switchToBackgroundMode = async () => {
  if (!taskId.value || isCompleted.value || isCancelling.value) {
    return
  }

  try {
    // 调用云函数将任务标记为后台模式
    await uniCloud.callFunction({
      name: 'update-task-background-mode',
      data: {
        taskId: taskId.value,
        backgroundMode: true
      }
    })
  } catch (error) {
    // 静默处理错误
  }
}

// 从后台模式恢复
const resumeFromBackgroundMode = async () => {
  if (!taskId.value) {
    return
  }

  try {
    // 调用云函数将任务标记为前台模式
    const result = await uniCloud.callFunction({
      name: 'update-task-background-mode',
      data: {
        taskId: taskId.value,
        backgroundMode: false
      }
    })

    if (result.result.code === 200) {
      // 检查返回的任务状态，如果任务已失败，更新UI
      const taskData = result.result.data
      if (taskData && taskData.status) {
        // 如果任务状态已改变，立即更新UI
        if (taskData.status === 'failed' || taskData.status === 'completed' || taskData.status === 'cancelled') {
          updateTaskProgress(taskData.status, taskData.progress || 0)
        }
      }
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 直接更新进度函数
const updateProgress = (newValue: number) => {
  progress.value = Math.round(Math.max(0, Math.min(100, newValue)) * 10) / 10
}

// 初始化进度状态
const initializeProgress = () => {
  progress.value = 0
  currentStep.value = 1
  statusText.value = '正在初始化...'
  isCompleted.value = false
  isCancelling.value = false
}

onUnmounted(() => {
  // 页面卸载时，将任务转入后台模式
  switchToBackgroundMode()
  stopPolling()
})

// 启动轮询
const startPolling = () => {
  if (isPolling.value || !taskId.value || isCompleted.value || isCancelling.value) {
    return
  }

  isPolling.value = true

  // 立即查询一次
  checkTaskStatus()

  // 设置定时器
  pollingTimer = setInterval(async () => {
    if (isCompleted.value || isCancelling.value) {
      stopPolling()
      return
    }

    if (isPageVisible.value) {
      await checkTaskStatus()
    }
  }, 10000)
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
  isPolling.value = false
}









// 开始任务状态轮询
const startTaskStatusPolling = async () => {
  try {
    statusText.value = '正在连接服务器...'

    // 设置初始进度
    updateProgress(3)

    statusText.value = '正在查询任务状态...'

    // 开始轮询
    startPolling()

  } catch (error) {
    statusText.value = '查询状态失败'
  }
}

// 检查任务状态
const checkTaskStatus = async () => {
  // 防止并发调用
  if (isCheckingStatus.value) {
    return
  }

  isCheckingStatus.value = true

  try {
    // 首先调用通用的任务状态查询
    const statusResult = await uniCloud.callFunction({
      name: 'get-task-status',
      data: {
        taskId: taskId.value
      }
    })

    // 如果任务处于需要特殊检查的阶段，进行额外处理
    if (statusResult.result.code === 200) {
      const currentStatus = statusResult.result.data.status

      // 对于 MPS 处理阶段，调用 poll-mps-tasks 获取最新状态
      if (currentStatus === 'extracting_audio' || currentStatus === 'merging') {
        try {
          const mpsResult = await uniCloud.callFunction({
            name: 'poll-mps-tasks',
            data: {
              taskId: taskId.value,
              mode: 'query'
            }
          })

          // 如果轮询返回了更新的状态，使用轮询结果
          if (mpsResult.result.code === 200 && mpsResult.result.data.status) {
            const mpsData = mpsResult.result.data
            if (mpsData.status === 'success') {
              // MPS任务成功，根据当前状态推进到下一阶段
              const currentStatus = statusResult.result.data.status
              if (currentStatus === 'extracting_audio') {
                // 音频提取完成，进入语音识别阶段
                updateTaskProgress('recognizing', 60)
              } else if (currentStatus === 'merging') {
                // 视频合成完成，任务完成
                updateTaskProgress('completed', 100)
              }
              return
            } else if (mpsData.status === 'failed') {
              // MPS任务失败
              updateTaskProgress('failed', 0)
              return
            }
            // 如果状态是 'running'，继续使用通用状态
          }
        } catch (mpsError) {
          // 轮询失败不影响主流程，继续使用通用状态
        }
      }
    }

    // 使用通用状态结果
    const result = statusResult

    if (result.result.code === 200) {
      const { status, progress: taskProgress, fileInfo } = result.result.data

      // 更新视频信息
      if (fileInfo) {
        videoInfo.value = {
          fileName: fileInfo.fileName || '',
          fileSize: fileInfo.fileSize || 0,
          duration: fileInfo.duration || 0
        }
      }

      // 根据状态更新界面（会自动检查并停止轮询）
      updateTaskProgress(status, taskProgress)

      // 如果任务已结束，确保轮询已停止
      if (isTaskFinished(status)) {
        stopPolling()
      }
    } else {
      statusText.value = '查询状态失败'
    }

  } catch (error) {
    statusText.value = '查询状态失败'
  } finally {
    isCheckingStatus.value = false
  }
}

// 检查是否为结束状态的辅助函数
const isTaskFinished = (status: string): boolean => {
  // 定义所有任务结束状态，当任务达到这些状态时应立即停止轮询
  const finishedStates = ['completed', 'failed', 'cancelled', 'deleted']
  return finishedStates.includes(status)
}

// 更新任务进度的辅助函数
const updateTaskProgress = (status: string, progressValue: number) => {
  // 首先检查是否为结束状态，如果是则停止轮询
  if (isTaskFinished(status)) {
    stopPolling()
  }

  switch (status) {
    case 'uploading':
      currentStep.value = 1
      updateProgress(progressValue || 25)
      statusText.value = '正在上传视频...'
      break
    case 'extracting_audio':
      currentStep.value = 2
      updateProgress(progressValue || 40)
      statusText.value = '正在提取音频...'
      break
    case 'recognizing':
      currentStep.value = 2
      updateProgress(progressValue || 60)
      statusText.value = '正在识别语音...'
      break
    case 'translating':
      currentStep.value = 3
      updateProgress(progressValue || 80)
      statusText.value = '正在生成字幕...'
      break
    case 'merging':
      currentStep.value = 4
      updateProgress(progressValue || 90)
      statusText.value = '正在合成视频...'
      break
    case 'completed':
      currentStep.value = 4
      updateProgress(100)
      statusText.value = '处理完成！'
      isCompleted.value = true
      break
    case 'failed':
      statusText.value = '处理失败'
      uni.showToast({
        title: '处理失败',
        icon: 'error'
      })
      break
    case 'cancelled':
      statusText.value = '任务已取消'
      updateProgress(0)
      break
    case 'deleted':
      statusText.value = '任务已删除'
      updateProgress(0)
      break
    default:
      statusText.value = '未知状态'
      break
  }
}

// 查看结果
const viewResult = () => {
  // 检查任务ID是否存在
  if (!taskId.value) {
    uni.showToast({
      title: '任务ID不存在',
      icon: 'none'
    })
    return
  }

  // 检查任务是否真的完成
  if (!isCompleted.value) {
    uni.showToast({
      title: '任务尚未完成',
      icon: 'none'
    })
    return
  }

  // 检查进度是否为100%
  if (progress.value < 100) {
    uni.showModal({
      title: '提示',
      content: '任务可能尚未完全完成，确定要查看结果吗？',
      success: (res) => {
        if (res.confirm) {
          navigateToResult()
        }
      }
    })
    return
  }

  // 直接跳转
  navigateToResult()
}

// 跳转到结果页面的辅助函数
const navigateToResult = () => {
  // 构建跳转URL，携带必要参数
  const url = `/pages/result/result?taskId=${encodeURIComponent(taskId.value)}`

  uni.navigateTo({
    url: url,
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    }
  })
}

// 取消处理 - 完善版
const cancelProcess = () => {
  // 防止重复点击
  if (isCancelling.value) {
    uni.showToast({
      title: '正在取消中...',
      icon: 'loading'
    })
    return
  }

  uni.showModal({
    title: '确认取消处理',
    content: '取消后当前处理进度将丢失，确定要取消吗？',
    confirmText: '确定取消',
    cancelText: '继续处理',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        performCancelTask()
      }
    }
  })
}

// 执行取消任务的具体操作
const performCancelTask = async () => {
  try {
    isCancelling.value = true
    statusText.value = '正在取消处理...'

    // 立即停止所有动画和轮询
    stopPolling()

    // 重置进度
    updateProgress(0)

    // 调用后端取消任务的云函数
    const cancelResult = await uniCloud.callFunction({
      name: 'cancel-task',
      data: {
        taskId: taskId.value
      }
    })

    if (cancelResult.result.code === 200) {
      // 取消成功
      statusText.value = '已取消处理'

      uni.showToast({
        title: '已取消处理',
        icon: 'success',
        duration: 2000
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack({
          fail: () => {
            // 如果返回失败，跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }, 2000)

    } else {
      // 取消失败，但仍然返回
      uni.showModal({
        title: '取消失败',
        content: '无法取消任务，但您可以离开此页面。处理可能会在后台继续。',
        showCancel: false,
        success: () => {
          uni.navigateBack({
            fail: () => {
              uni.reLaunch({
                url: '/pages/index/index'
              })
            }
          })
        }
      })
    }

  } catch (error) {
    uni.showModal({
      title: '取消失败',
      content: '网络错误，无法取消任务。您可以离开此页面，处理可能会在后台继续。',
      showCancel: false,
      success: () => {
        uni.navigateBack({
          fail: () => {
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }
    })

  } finally {
    isCancelling.value = false
  }
}
</script>

<style scoped>
.process-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 现代化头部区域 */
.hero-section {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.status-indicator {
  margin-right: 24rpx;
  position: relative;
}

.pulse-ring {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
  top: -10rpx;
  left: -10rpx;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.hero-text {
  flex: 1;
}

.hero-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
}

.hero-progress {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 现代化进度条 */
.modern-progress-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
  z-index: 1;
}

.progress-track {
  flex: 1;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
}

.progress-fill-modern {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ee5a52);
  border-radius: 8rpx;
  position: relative;
  transition: width 0.6s ease;
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-percentage {
  font-size: 28rpx;
  font-weight: 700;
  color: white;
  min-width: 80rpx;
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* 步骤卡片 */
.steps-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.card-header-modern {
  margin-bottom: 32rpx;
  text-align: center;
}

.card-title-modern {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.card-subtitle {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.step-modern {
  display: flex;
  align-items: flex-start;
  position: relative;
  opacity: 0.4;
  transition: all 0.3s ease;
}

.step-modern.active {
  opacity: 1;
}

.step-modern.current {
  opacity: 1;
  transform: scale(1.02);
}

.step-modern.completed {
  opacity: 0.7;
}

.step-indicator-modern {
  position: relative;
  margin-right: 24rpx;
}

.step-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.step-modern.active .step-circle {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
}

.step-modern:not(.active) .step-circle {
  background: #f3f4f6;
  border: 2rpx solid #e5e7eb;
}

.step-icon-modern {
  font-size: 32rpx;
}

.step-ripple {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 100rpx;
  height: 100rpx;
  border: 4rpx solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.step-line {
  position: absolute;
  top: 80rpx;
  left: 40rpx;
  width: 4rpx;
  height: 52rpx;
  background: linear-gradient(180deg, #e0e7ff, #c7d2fe);
  border-radius: 2rpx;
  z-index: 1;
}

.step-content-modern {
  flex: 1;
  padding-top: 16rpx;
}

.step-title-modern {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.step-desc-modern {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 视频信息卡片 */
.info-card-modern {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.info-card-modern .card-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.info-badge {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-text {
  font-size: 24rpx;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item-modern {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 2rpx solid #f1f5f9;
  transition: all 0.3s ease;
}

.info-item-modern:hover {
  background: #f1f5f9;
  border-color: #e2e8f0;
}

.info-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.info-icon {
  font-size: 28rpx;
}

.info-content {
  flex: 1;
}

.info-label-modern {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
  font-weight: 500;
}

.info-value-modern {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

/* 现代化操作区域 */
.action-section-modern {
  display: flex;
  justify-content: center;
  padding: 24rpx 0;
}

.btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  min-width: 240rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary-modern {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.3);
}

.btn-primary-modern:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.4);
}

.btn-secondary-modern {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(245, 158, 11, 0.3);
}

.btn-secondary-modern:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.4);
}

.btn-disabled-modern {
  background: linear-gradient(135deg, #d1d5db, #9ca3af);
  color: #6b7280;
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-icon-modern {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.btn-text-modern {
  font-size: 30rpx;
  font-weight: 600;
}

/* 底部空间 */
.bottom-space {
  height: 80rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .hero-header {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .status-indicator {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
  
  .info-grid {
    gap: 16rpx;
  }
  
  .btn-modern {
    min-width: 200rpx;
    padding: 20rpx 40rpx;
  }
}
</style>
