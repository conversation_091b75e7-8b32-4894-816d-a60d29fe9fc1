{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.vue": "vue", "*.nvue": "vue"}, "emmet.includeLanguages": {"vue-html": "html", "vue": "html"}, "emmet.triggerExpansionOnTab": true, "vetur.format.defaultFormatter.html": "js-beautify-html", "vetur.format.defaultFormatter.css": "prettier", "vetur.format.defaultFormatter.js": "prettier", "vetur.format.defaultFormatter.ts": "prettier", "vetur.format.options.tabSize": 2, "vetur.format.options.useTabs": false, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.eol": "\n", "search.exclude": {"**/node_modules": true, "**/dist": true, "**/unpackage": true}, "files.exclude": {"**/unpackage": true}}