# 智能字幕胶囊

这是一个基于 uni-app 和 uniCloud 的微信小程序，专注于视频字幕生成功能。当前版本保留了完整的用户界面，但后端视频处理功能正在开发中。

## 功能特性

### 🔐 用户系统（已完成）
- **微信授权登录** - 安全便捷的微信一键登录
- **用户信息管理** - 自动获取并保存用户昵称、头像
- **设备绑定** - 设备唯一标识与用户账号绑定
- **会话管理** - 登录状态持久化保存

### 📱 界面功能（已完成）
- **首页** - 产品介绍和快速开始入口
- **视频上传** - 视频文件选择和上传界面
- **处理进度** - 实时显示处理进度和状态
- **处理结果** - 视频预览和字幕下载
- **历史记录** - 查看和管理历史任务
- **个人中心** - 用户信息和设置管理

### ☁️ 云函数（部分完成）
- ✅ `get-wechat-openid` - 获取微信用户 openid
- ✅ `update-user-info` - 更新用户信息
- 🚧 视频处理相关云函数（开发中）

### 🗄️ 数据库（部分完成）
- ✅ `users` - 用户信息表
- 🚧 `tasks` - 视频处理任务表（开发中）

## 项目结构

```
├── src/
│   ├── pages/
│   │   ├── index/           # 首页
│   │   ├── upload/          # 视频上传页面
│   │   ├── process/         # 处理进度页面
│   │   ├── result/          # 处理结果页面
│   │   ├── history/         # 历史记录页面
│   │   └── profile/         # 个人中心页面
│   ├── utils/
│   │   ├── api.ts          # API 调用封装
│   │   └── common.ts       # 通用工具函数
│   └── ...
├── uniCloud-aliyun/
│   ├── cloudfunctions/     # 云函数
│   │   ├── get-wechat-openid/    # 获取微信openid
│   │   └── update-user-info/     # 更新用户信息
│   └── database/           # 数据库表结构
│       └── users.schema.json     # 用户表
└── ...
```

## 开发状态

### ✅ 已完成功能
1. **完整的用户界面** - 所有页面和交互功能
2. **微信登录系统** - 完整的用户身份验证
3. **用户信息管理** - 用户资料的获取和保存
4. **页面导航和路由** - 完整的应用导航体系

### 🚧 开发中功能
1. **视频上传处理** - 文件上传和云存储
2. **AI语音识别** - 视频语音转文字
3. **字幕生成合成** - 字幕文件生成和视频合成
4. **任务状态管理** - 处理任务的状态跟踪

## 开发指南

### 环境要求
- Node.js 16+
- HBuilderX 或 VS Code
- 微信开发者工具

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
npm run dev:mp-weixin
```

### 类型检查
```bash
npm run type-check
```

### 配置说明

1. **微信小程序配置**
   - 在 `src\uni_modules\uni-config-center\uniCloud\cloudfunctions\common\uni-config-center\wechat-mp\config.json` 中配置你的 AppID 和 AppSecret
   - 在 `src/manifest.json` 中更新微信小程序的 AppID

2. **阿里云VOD配置**
   - 在 `src\uni_modules\uni-config-center\uniCloud\cloudfunctions\common\uni-config-center\aliyun-vod\config.json` 中配置阿里云访问密钥

3. **uniCloud 配置**
   - 在 HBuilderX 中关联你的 uniCloud 服务空间
   - 上传云函数、公共模块和数据库表结构

## 使用说明

### 当前可用功能
1. **用户登录** - 完整的微信登录流程
2. **界面浏览** - 所有页面都可以正常访问和浏览
3. **用户中心** - 查看和管理用户信息

### 开发中功能
- 点击视频处理相关功能时会显示"功能开发中"提示
- 界面完整保留，便于后续功能开发和集成

## 技术栈

- **前端框架**: uni-app + Vue 3 + TypeScript
- **云服务**: uniCloud (阿里云)
- **小程序**: 微信小程序
- **数据库**: uniCloud DB

## 许可证

MIT License
