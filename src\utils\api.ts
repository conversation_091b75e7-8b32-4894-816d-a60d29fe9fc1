/**
 * API调用工具类
 * 封装uniCloud云函数调用
 */

export interface CloudFunctionResult<T = any> {
  result: T
  requestID: string
  errMsg: string
}



export interface UserInfo {
  _id: string
  openid: string
  unionid?: string
  deviceId: string
  sessionKey: string
  nickname?: string
  avatar?: string
  createTime: string
  updateTime?: string
  lastLoginTime: string
  status: 'active' | 'inactive' | 'banned'
}

export interface WechatLoginResult {
  errCode: number
  errMsg: string
  data?: {
    openid: string
    userId: string
    sessionKey: string
    unionid?: string
    isNewUser: boolean
  }
}

/**
 * 云函数调用封装 - 使用uniCloud
 */
export const callCloudFunction = <T = any>(
  name: string,
  data?: any
): Promise<T> => {
  return new Promise((resolve, reject) => {
    // 使用uniCloud调用云函数
    uniCloud.callFunction({
      name,
      data,
      success: (res: any) => {
        resolve(res.result)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 获取微信用户openid
 */
export const getWechatOpenid = (code: string): Promise<WechatLoginResult> => {
  const deviceId = getDeviceId()
  return callCloudFunction('get-wechat-openid', { code, deviceId })
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (openid: string, nickname?: string, avatar?: string): Promise<void> => {
  const deviceId = getDeviceId()
  return callCloudFunction('update-user-info', { openid, nickname, avatar, deviceId })
}

/**
 * 简单哈希函数（用于生成设备指纹）
 */
const simpleHash = (str: string): string => {
  let hash = 0
  if (str.length === 0) return hash.toString()

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  return Math.abs(hash).toString(36)
}

/**
 * 生成设备唯一标识
 */
export const generateDeviceId = (): string => {
  try {
    const systemInfo = uni.getSystemInfoSync()

    // 构建设备信息字符串
    const deviceString = [
      systemInfo.platform || 'unknown',
      systemInfo.model || 'unknown',
      systemInfo.system || 'unknown',
      systemInfo.version || 'unknown',
      systemInfo.screenWidth || '0',
      systemInfo.screenHeight || '0'
    ].join('|')

    // 生成设备指纹
    const deviceHash = simpleHash(deviceString)
    const deviceFingerprint = 'dev_' + deviceHash + '_' + Date.now().toString(36)

    // 存储到本地
    uni.setStorageSync('deviceId', deviceFingerprint)

    return deviceFingerprint
  } catch (error) {
    // 降级方案：使用时间戳和随机数
    const fallbackId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    uni.setStorageSync('deviceId', fallbackId)
    return fallbackId
  }
}

/**
 * 获取设备ID
 */
export const getDeviceId = (): string => {
  let deviceId = uni.getStorageSync('deviceId')
  if (!deviceId) {
    deviceId = generateDeviceId()
  }
  return deviceId
}
