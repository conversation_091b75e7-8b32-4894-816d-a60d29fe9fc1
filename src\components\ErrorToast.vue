<template>
  <view v-if="visible" class="error-toast-overlay" @click="handleOverlayClick">
    <view class="error-toast" :class="{ 'toast-entering': entering, 'toast-leaving': leaving }">
      <view class="error-header">
        <view class="error-icon" :class="getIconClass()">
          {{ getIcon() }}
        </view>
        <view class="error-info">
          <text class="error-title">{{ getTitle() }}</text>
          <text class="error-time">{{ formatTime(timestamp) }}</text>
        </view>
        <view class="close-btn" @click="close">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <view class="error-content">
        <text class="error-message">{{ message }}</text>

        <!-- 详细错误信息 -->
        <view v-if="details" class="error-details">
          <view class="details-header" @click="toggleDetails">
            <text class="details-title">错误详情</text>
            <text class="details-arrow" :class="{ 'arrow-expanded': showDetails }">▼</text>
          </view>
          <view v-if="showDetails" class="details-content">
            <text class="details-text">{{ details }}</text>
          </view>
        </view>

        <!-- 建议操作 -->
        <view v-if="suggestions && suggestions.length > 0" class="error-suggestions">
          <text class="suggestions-title">建议解决方案：</text>
          <view class="suggestions-list">
            <text v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
              {{ index + 1 }}. {{ suggestion }}
            </text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="error-actions">
        <button v-if="showRetry" class="action-btn retry-btn" @click="handleRetry">
          <text class="btn-icon">🔄</text>
          <text class="btn-text">重试</text>
        </button>

        <button class="action-btn close-btn-alt" @click="close">
          <text class="btn-text">关闭</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, toRefs, watch } from "vue";
import { formatTime } from "@/utils/common";

// Props
interface Props {
  visible: boolean;
  type?: "error" | "warning" | "info";
  title?: string;
  message: string;
  details?: string;
  suggestions?: string[];
  showRetry?: boolean;
  autoClose?: boolean;
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  type: "error",
  title: "",
  suggestions: () => [],
  showRetry: false,
  autoClose: false,
  duration: 5000,
});

// Emits
interface Emits {
  (e: "close"): void;
  (e: "retry"): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const entering = ref(false);
const leaving = ref(false);
const showDetails = ref(false);
const timestamp = ref(new Date());
const autoCloseTimer = ref<number | null>(null);

// 计算属性
const getIcon = () => {
  const icons = {
    error: "❌",
    warning: "⚠️",
    info: "ℹ️",
  };
  return icons[props.type] || icons.error;
};

const getIconClass = () => {
  return `${props.type}-icon`;
};

const getTitle = () => {
  if (props.title) return props.title;

  const titles = {
    error: "操作失败",
    warning: "注意",
    info: "提示",
  };
  return titles[props.type] || titles.error;
};


// 切换详情显示
const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

// 处理遮罩点击
const handleOverlayClick = (e: any) => {
  // 只有点击遮罩本身才关闭
  if (e.target.classList.contains("error-toast-overlay")) {
    close();
  }
};

// 关闭提示
const close = () => {
  leaving.value = true;

  setTimeout(() => {
    emit("close");
    leaving.value = false;
  }, 300);

  if (autoCloseTimer.value) {
    clearTimeout(autoCloseTimer.value);
    autoCloseTimer.value = null;
  }
};

// 处理重试
const handleRetry = () => {
  emit("retry");
  close();
};



// 设置自动关闭
const setupAutoClose = () => {
  if (props.autoClose && props.duration > 0) {
    autoCloseTimer.value = setTimeout(() => {
      close();
    }, props.duration);
  }
};

// 生命周期
onMounted(() => {
  if (props.visible) {
    entering.value = true;
    setTimeout(() => {
      entering.value = false;
    }, 300);

    setupAutoClose();
  }
});

onUnmounted(() => {
  if (autoCloseTimer.value) {
    clearTimeout(autoCloseTimer.value);
  }
});

// 监听visible变化
const { visible } = toRefs(props);
watch(visible, (newVisible) => {
  if (newVisible) {
    timestamp.value = new Date();
    entering.value = true;
    setTimeout(() => {
      entering.value = false;
    }, 300);

    setupAutoClose();
  }
});
</script>

<style scoped>
.error-toast-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 32rpx;
}

.error-toast {
  background: white;
  border-radius: 24rpx;
  max-width: 640rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  opacity: 1;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.error-toast.toast-entering {
  transform: translateY(40rpx) scale(0.95);
  opacity: 0;
}

.error-toast.toast-leaving {
  transform: translateY(-40rpx) scale(0.95);
  opacity: 0;
}

/* ==================== 头部区域 ==================== */
.error-header {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.error-icon {
  font-size: 32rpx;
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.error-icon.error-icon {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.error-icon.warning-icon {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
}

.error-icon.info-icon {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.error-info {
  flex: 1;
}

.error-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.error-time {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
}

.close-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.close-icon {
  font-size: 18rpx;
  color: #6b7280;
  font-weight: bold;
}

/* ==================== 内容区域 ==================== */
.error-content {
  padding: 16rpx 24rpx;
}

.error-message {
  display: block;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

/* ==================== 错误详情 ==================== */
.error-details {
  margin-bottom: 16rpx;
  border: 1rpx solid #f3f4f6;
  border-radius: 8rpx;
  overflow: hidden;
}

.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background: #f8fafc;
  cursor: pointer;
  transition: background 0.2s ease;
}

.details-header:active {
  background: #f1f5f9;
}

.details-title {
  font-size: 24rpx;
  color: #4b5563;
  font-weight: 500;
}

.details-arrow {
  font-size: 18rpx;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.details-arrow.arrow-expanded {
  transform: rotate(180deg);
}

.details-content {
  padding: 16rpx;
  background: white;
  border-top: 1rpx solid #f3f4f6;
}

.details-text {
  font-size: 22rpx;
  color: #6b7280;
  line-height: 1.5;
  word-break: break-all;
}

/* ==================== 建议区域 ==================== */
.error-suggestions {
  background: #f0f9ff;
  border: 1rpx solid #e0f2fe;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.suggestions-title {
  display: block;
  font-size: 24rpx;
  color: #0369a1;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-item {
  font-size: 22rpx;
  color: #0284c7;
  line-height: 1.4;
}

/* ==================== 操作按钮 ==================== */
.error-actions {
  display: flex;
  gap: 12rpx;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f1f5f9;
}

.action-btn {
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: translateY(-1rpx);
}

.retry-btn {
  flex: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}

.retry-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.4);
}



.close-btn-alt {
  flex: 1;
  background: #f3f4f6;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}

.close-btn-alt:active {
  background: #e5e7eb;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  color: inherit;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .error-toast-overlay {
    padding: 20rpx;
  }

  .error-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}
</style>
