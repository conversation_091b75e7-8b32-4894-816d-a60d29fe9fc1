{"bsonType": "object", "description": "用户信息表", "required": ["openid", "deviceId", "createTime"], "properties": {"_id": {"description": "记录ID"}, "openid": {"bsonType": "string", "description": "微信用户openid", "title": "微信OpenID"}, "unionid": {"bsonType": "string", "description": "微信用户unionid", "title": "微信UnionID"}, "deviceId": {"bsonType": "string", "description": "设备唯一标识", "title": "设备ID"}, "sessionKey": {"bsonType": "string", "description": "微信会话密钥", "title": "会话密钥"}, "nickname": {"bsonType": "string", "description": "用户昵称", "title": "昵称"}, "avatar": {"bsonType": "string", "description": "用户头像URL", "title": "头像"}, "createTime": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间"}, "updateTime": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间"}, "lastLoginTime": {"bsonType": "timestamp", "description": "最后登录时间", "title": "最后登录时间"}, "lastLoginIP": {"bsonType": "string", "description": "最后登录IP", "title": "最后登录IP"}, "lastLoginUA": {"bsonType": "string", "description": "最后登录用户代理", "title": "最后登录UA"}, "lastActiveTime": {"bsonType": "timestamp", "description": "最后活跃时间", "title": "最后活跃时间"}, "lastActiveIP": {"bsonType": "string", "description": "最后活跃IP", "title": "最后活跃IP"}, "lastActiveUA": {"bsonType": "string", "description": "最后活跃用户代理", "title": "最后活跃UA"}, "status": {"bsonType": "string", "description": "用户状态", "title": "状态", "enum": ["active", "inactive", "banned"], "enumDesc": ["正常", "未激活", "已封禁"], "default": "active"}}, "permission": {"read": true, "create": true, "update": false, "delete": false}}