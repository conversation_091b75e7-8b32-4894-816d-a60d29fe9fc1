/**
 * 现代化设计系统 - 智能字幕胶囊
 * 基于Material Design 3.0和现代设计原则
 */

/* ==================== 颜色系统 ==================== */

/* 主色调 - 现代蓝紫色渐变 */
$primary-50: #f0f4ff;
$primary-100: #e0e7ff;
$primary-200: #c7d2fe;
$primary-300: #a5b4fc;
$primary-400: #818cf8;
$primary-500: #6366f1;
$primary-600: #4f46e5;
$primary-700: #4338ca;
$primary-800: #3730a3;
$primary-900: #312e81;

/* 中性色 - 现代灰色系统 */
$neutral-50: #fafafa;
$neutral-100: #f5f5f5;
$neutral-200: #e5e5e5;
$neutral-300: #d4d4d4;
$neutral-400: #a3a3a3;
$neutral-500: #737373;
$neutral-600: #525252;
$neutral-700: #404040;
$neutral-800: #262626;
$neutral-900: #171717;

/* 语义化颜色 */
$success-50: #f0fdf4;
$success-500: #22c55e;
$success-600: #16a34a;

$warning-50: #fffbeb;
$warning-500: #f59e0b;
$warning-600: #d97706;

$error-50: #fef2f2;
$error-500: #ef4444;
$error-600: #dc2626;

$info-50: #eff6ff;
$info-500: #3b82f6;
$info-600: #2563eb;

/* 兼容旧版本的uni-app变量 */
$uni-color-primary: $primary-600;
$uni-color-success: $success-500;
$uni-color-warning: $warning-500;
$uni-color-error: $error-500;

/* 文字颜色系统 */
$uni-text-color: $neutral-900; // 主要文字
$uni-text-color-secondary: $neutral-600; // 次要文字
$uni-text-color-tertiary: $neutral-400; // 辅助文字
$uni-text-color-inverse: #ffffff; // 反色文字
$uni-text-color-placeholder: $neutral-400;
$uni-text-color-disable: $neutral-300;

/* 背景颜色系统 */
$uni-bg-color: #ffffff;
$uni-bg-color-page: $neutral-50; // 页面背景
$uni-bg-color-grey: $neutral-100;
$uni-bg-color-hover: $neutral-100;
$uni-bg-color-active: $neutral-200;
$uni-bg-color-mask: rgba(0, 0, 0, 0.5);

/* 边框颜色 */
$uni-border-color: $neutral-200;
$uni-border-color-light: $neutral-100;

/* ==================== 字体系统 ==================== */

/* 字体族 */
$font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

/* 字体大小 - 基于8px网格系统 */
$font-size-xs: 24rpx;   // 12px
$font-size-sm: 28rpx;   // 14px
$font-size-base: 32rpx; // 16px
$font-size-lg: 36rpx;   // 18px
$font-size-xl: 40rpx;   // 20px
$font-size-2xl: 48rpx;  // 24px
$font-size-3xl: 60rpx;  // 30px
$font-size-4xl: 72rpx;  // 36px

/* 字体权重 */
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* 行高 */
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

/* 兼容旧版本 */
$uni-font-size-sm: $font-size-sm;
$uni-font-size-base: $font-size-base;
$uni-font-size-lg: $font-size-lg;

/* ==================== 间距系统 - 8px网格 ==================== */

/* 基础间距单位 */
$spacing-1: 8rpx;   // 4px
$spacing-2: 16rpx;  // 8px
$spacing-3: 24rpx;  // 12px
$spacing-4: 32rpx;  // 16px
$spacing-5: 40rpx;  // 20px
$spacing-6: 48rpx;  // 24px
$spacing-8: 64rpx;  // 32px
$spacing-10: 80rpx; // 40px
$spacing-12: 96rpx; // 48px
$spacing-16: 128rpx; // 64px
$spacing-20: 160rpx; // 80px
$spacing-24: 192rpx; // 96px

/* 兼容旧版本 */
$uni-spacing-row-sm: $spacing-2;
$uni-spacing-row-base: $spacing-4;
$uni-spacing-row-lg: $spacing-6;
$uni-spacing-col-sm: $spacing-1;
$uni-spacing-col-base: $spacing-2;
$uni-spacing-col-lg: $spacing-3;

/* ==================== 圆角系统 ==================== */

$radius-none: 0;
$radius-sm: 8rpx;   // 4px
$radius-base: 16rpx; // 8px
$radius-md: 24rpx;  // 12px
$radius-lg: 32rpx;  // 16px
$radius-xl: 48rpx;  // 24px
$radius-2xl: 64rpx; // 32px
$radius-full: 9999rpx;

/* 兼容旧版本 */
$uni-border-radius-sm: $radius-sm;
$uni-border-radius-base: $radius-base;
$uni-border-radius-lg: $radius-lg;
$uni-border-radius-circle: $radius-full;

/* ==================== 阴影系统 ==================== */

$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
$shadow-base: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-lg: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);
$shadow-xl: 0 32rpx 128rpx rgba(0, 0, 0, 0.24);

/* ==================== 其他系统变量 ==================== */

/* 透明度 */
$opacity-disabled: 0.4;
$opacity-loading: 0.6;

/* 过渡动画 */
$transition-fast: 0.15s ease-out;
$transition-base: 0.2s ease-out;
$transition-slow: 0.3s ease-out;

/* Z-index层级 */
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

/* 兼容旧版本 */
$uni-opacity-disabled: $opacity-disabled;
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* ==================== 全局样式重置 ==================== */

/* 页面基础样式 */
page {
  font-family: $font-family-sans;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $uni-text-color;
  background-color: $uni-bg-color-page;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用容器 */
.container {
  padding: 0 $spacing-4;
  max-width: 750rpx;
  margin: 0 auto;
}

.container-fluid {
  padding: 0 $spacing-4;
  width: 100%;
}

/* ==================== 现代化工具类 ==================== */

/* 文字样式 */
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }

.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }

.text-primary { color: $primary-600; }
.text-secondary { color: $uni-text-color-secondary; }
.text-tertiary { color: $uni-text-color-tertiary; }
.text-success { color: $success-500; }
.text-warning { color: $warning-500; }
.text-error { color: $error-500; }
.text-white { color: #ffffff; }

/* 背景颜色 */
.bg-primary { background-color: $primary-600; }
.bg-primary-light { background-color: $primary-100; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: $neutral-50; }
.bg-gray-100 { background-color: $neutral-100; }
.bg-success { background-color: $success-500; }
.bg-warning { background-color: $warning-500; }
.bg-error { background-color: $error-500; }

/* 圆角 */
.rounded-none { border-radius: $radius-none; }
.rounded-sm { border-radius: $radius-sm; }
.rounded { border-radius: $radius-base; }
.rounded-md { border-radius: $radius-md; }
.rounded-lg { border-radius: $radius-lg; }
.rounded-xl { border-radius: $radius-xl; }
.rounded-2xl { border-radius: $radius-2xl; }
.rounded-full { border-radius: $radius-full; }

/* 阴影 */
.shadow-sm { box-shadow: $shadow-sm; }
.shadow { box-shadow: $shadow-base; }
.shadow-md { box-shadow: $shadow-md; }
.shadow-lg { box-shadow: $shadow-lg; }
.shadow-xl { box-shadow: $shadow-xl; }
.shadow-none { box-shadow: none; }

/* 间距 - Padding */
.p-0 { padding: 0; }
.p-1 { padding: $spacing-1; }
.p-2 { padding: $spacing-2; }
.p-3 { padding: $spacing-3; }
.p-4 { padding: $spacing-4; }
.p-5 { padding: $spacing-5; }
.p-6 { padding: $spacing-6; }
.p-8 { padding: $spacing-8; }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: $spacing-1; padding-right: $spacing-1; }
.px-2 { padding-left: $spacing-2; padding-right: $spacing-2; }
.px-3 { padding-left: $spacing-3; padding-right: $spacing-3; }
.px-4 { padding-left: $spacing-4; padding-right: $spacing-4; }
.px-5 { padding-left: $spacing-5; padding-right: $spacing-5; }
.px-6 { padding-left: $spacing-6; padding-right: $spacing-6; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: $spacing-1; padding-bottom: $spacing-1; }
.py-2 { padding-top: $spacing-2; padding-bottom: $spacing-2; }
.py-3 { padding-top: $spacing-3; padding-bottom: $spacing-3; }
.py-4 { padding-top: $spacing-4; padding-bottom: $spacing-4; }
.py-5 { padding-top: $spacing-5; padding-bottom: $spacing-5; }
.py-6 { padding-top: $spacing-6; padding-bottom: $spacing-6; }

/* 间距 - Margin */
.m-0 { margin: 0; }
.m-1 { margin: $spacing-1; }
.m-2 { margin: $spacing-2; }
.m-3 { margin: $spacing-3; }
.m-4 { margin: $spacing-4; }
.m-5 { margin: $spacing-5; }
.m-6 { margin: $spacing-6; }
.m-8 { margin: $spacing-8; }

.mx-auto { margin-left: auto; margin-right: auto; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: $spacing-1; }
.mb-2 { margin-bottom: $spacing-2; }
.mb-3 { margin-bottom: $spacing-3; }
.mb-4 { margin-bottom: $spacing-4; }
.mb-5 { margin-bottom: $spacing-5; }
.mb-6 { margin-bottom: $spacing-6; }
.mb-8 { margin-bottom: $spacing-8; }

/* Flexbox布局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 显示/隐藏 */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ==================== 现代化组件样式 ==================== */

/* 现代化按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-3 $spacing-6;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  line-height: 1;
  border-radius: $radius-lg;
  border: none;
  cursor: pointer;
  transition: all $transition-base;
  text-decoration: none;

  &:disabled {
    opacity: $opacity-disabled;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: linear-gradient(135deg, $primary-600, $primary-700);
  color: white;
  box-shadow: $shadow-sm;

  &:hover {
    background: linear-gradient(135deg, $primary-700, $primary-800);
    box-shadow: $shadow-md;
    transform: translateY(-1rpx);
  }

  &:active {
    transform: translateY(0);
    box-shadow: $shadow-sm;
  }
}

.btn-secondary {
  background-color: $neutral-100;
  color: $neutral-700;
  border: 2rpx solid $neutral-200;

  &:hover {
    background-color: $neutral-200;
    border-color: $neutral-300;
  }
}

.btn-outline {
  background-color: transparent;
  color: $primary-600;
  border: 2rpx solid $primary-600;

  &:hover {
    background-color: $primary-50;
  }
}

.btn-ghost {
  background-color: transparent;
  color: $primary-600;

  &:hover {
    background-color: $primary-50;
  }
}

.btn-sm {
  padding: $spacing-2 $spacing-4;
  font-size: $font-size-sm;
}

.btn-lg {
  padding: $spacing-4 $spacing-8;
  font-size: $font-size-lg;
}

/* 现代化卡片 */
.card {
  background-color: white;
  border-radius: $radius-xl;
  box-shadow: $shadow-sm;
  overflow: hidden;
  transition: all $transition-base;

  &:hover {
    box-shadow: $shadow-md;
    transform: translateY(-2rpx);
  }
}

.card-body {
  padding: $spacing-6;
}

.card-header {
  padding: $spacing-6 $spacing-6 0;
}

.card-footer {
  padding: 0 $spacing-6 $spacing-6;
}

/* 现代化输入框 */
.input {
  width: 100%;
  padding: $spacing-4;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $uni-text-color;
  background-color: white;
  border: 2rpx solid $neutral-200;
  border-radius: $radius-lg;
  transition: all $transition-base;

}

.input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 6rpx rgba(99, 102, 241, 0.1);
}

/* 现代化标签 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: $spacing-1 $spacing-3;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  border-radius: $radius-full;

}

.tag.tag-primary {
  background-color: #e0e7ff;
  color: #4338ca;
}

.tag.tag-success {
  background-color: #ecfdf5;
  color: #059669;
}

.tag.tag-warning {
  background-color: #fffbeb;
  color: #d97706;
}

.tag.tag-error {
  background-color: #fef2f2;
  color: #dc2626;
}

/* ==================== 响应式布局工具类 ==================== */

/* 响应式容器 */
.container-responsive {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 32rpx;
}

/* 响应式网格 */
.grid-responsive {
  display: grid;
  gap: $spacing-4;

}

.grid-responsive.grid-1 {
  grid-template-columns: 1fr;
}

.grid-responsive.grid-2 {
  grid-template-columns: 1fr;
}

.grid-responsive.grid-3 {
  grid-template-columns: 1fr;
}

.grid-responsive.grid-4 {
  grid-template-columns: repeat(2, 1fr);
}
}

/* 响应式文字大小 */
.text-responsive {
  font-size: 26rpx;
}

.title-responsive {
  font-size: 40rpx;
}

/* 响应式间距 */
.p-responsive {
  padding: 24rpx;
}

.m-responsive {
  margin: 24rpx;
}

/* 响应式显示/隐藏 - 简化版本 */
.hidden-sm {
  display: none;
}

.hidden-md {
  display: none;
}

.show-sm {
  display: block;
}

.show-md {
  display: block;
}

/* ==================== 动画和过渡效果 ==================== */

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* 悬停效果 - 微信小程序兼容版本 */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:active {
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 64rpx rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: all 0.3s ease;
}

.hover-scale:active {
  transform: scale(1.02);
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 骨架屏效果 */
@keyframes skeleton {
  0% {
    background-position: -200rpx 0;
  }
  100% {
    background-position: calc(200rpx + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400rpx 100%;
  animation: skeleton 1.2s ease-in-out infinite;
}

/* ==================== TabBar 样式优化 ==================== */

/* 由于使用纯文字tabBar，我们可以通过全局样式来美化 */
.uni-tabbar {
  border-top: 2rpx solid #f3f4f6;
  box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.uni-tabbar-item {
  position: relative;
}

.uni-tabbar-item.uni-tabbar-item-active::before {
  content: '';
  position: absolute;
  top: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 3rpx;
}

.uni-tabbar-text {
  font-weight: 500;
  font-size: 24rpx;
}

.uni-tabbar-text.uni-tabbar-text-active {
  font-weight: 600;
}