{"name": "uni-cloud-router", "version": "0.0.10", "description": "uni-cloud router", "main": "dist/index.js", "author": "fxy060608", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/fxy060608/uni-cloud-router.git"}, "origin-plugin-dev-name": "uni-cloud-router", "origin-plugin-version": "1.0.3", "plugin-dev-name": "uni-cloud-router", "plugin-version": "1.0.3", "dependencies": {"uni-id-common": "file:../../../../../uni-id-common/uniCloud/cloudfunctions/common/uni-id-common", "uni-open-bridge-common": "file:../../../../../uni-open-bridge-common/uniCloud/cloudfunctions/common/uni-open-bridge-common", "uni-config-center": "file:../../../../../uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}}