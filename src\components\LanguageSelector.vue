<template>
  <view class="language-selector">
    <!-- 源语言选择器 -->
    <view class="selector-group">
      <view class="selector-label">
        <text class="label-text">源语言</text>
        <text class="label-hint">自动识别或手动选择</text>
      </view>
      <picker
        :value="sourceLanguageIndex"
        :range="sourceLanguageOptions"
        range-key="name"
        @change="onSourceLanguageChange"
        mode="selector"
      >
        <view class="selector-container">
          <view class="selector-display">
            <view class="selector-left">
              <text class="selector-icon">🎤</text>
              <text class="selector-text">{{ sourceLanguageDisplay }}</text>
            </view>
            <text class="dropdown-icon">▼</text>
          </view>
        </view>
      </picker>
    </view>

    <!-- 目标语言选择器 -->
    <view class="selector-group">
      <view class="selector-label">
        <text class="label-text">目标语言</text>
        <text class="label-hint">翻译后的目标语言</text>
      </view>
      <picker
        :value="targetLanguageIndex"
        :range="targetLanguageOptions"
        range-key="name"
        @change="onTargetLanguageChange"
        mode="selector"
      >
        <view class="selector-container">
          <view class="selector-display">
            <view class="selector-left">
              <text class="selector-icon">🌍</text>
              <text class="selector-text">{{ targetLanguageDisplay }}</text>
            </view>
            <text class="dropdown-icon">▼</text>
          </view>
        </view>
      </picker>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义 props
interface Props {
  sourceLanguage?: string
  targetLanguage?: string
}

const props = withDefaults(defineProps<Props>(), {
  sourceLanguage: 'auto',
  targetLanguage: 'zh'
})

// 定义 emits
const emit = defineEmits<{
  'update:sourceLanguage': [value: string]
  'update:targetLanguage': [value: string]
}>()

// 语言选项配置
const sourceLanguageOptions = [
  { code: 'auto', name: '自动识别' },
  { code: 'en', name: '英语' },
  { code: 'ja', name: '日语' },
  { code: 'ko', name: '韩语' },
  { code: 'de', name: '德语' },
  { code: 'fr', name: '法语' },
  { code: 'ru', name: '俄语' },
  { code: 'zh', name: '中文' }
]

const targetLanguageOptions = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: '英语' },
  { code: 'ja', name: '日语' },
  { code: 'ko', name: '韩语' },
  { code: 'de', name: '德语' },
  { code: 'fr', name: '法语' },
  { code: 'ru', name: '俄语' }
]

// 计算当前选中的索引
const sourceLanguageIndex = computed(() => {
  return sourceLanguageOptions.findIndex(option => option.code === props.sourceLanguage)
})

const targetLanguageIndex = computed(() => {
  return targetLanguageOptions.findIndex(option => option.code === props.targetLanguage)
})

// 计算显示文本
const sourceLanguageDisplay = computed(() => {
  const option = sourceLanguageOptions.find(option => option.code === props.sourceLanguage)
  return option?.name || '自动识别'
})

const targetLanguageDisplay = computed(() => {
  const option = targetLanguageOptions.find(option => option.code === props.targetLanguage)
  return option?.name || '中文'
})

// 处理选择器变化
const onSourceLanguageChange = (e: any) => {
  const selectedOption = sourceLanguageOptions[e.detail.value]
  emit('update:sourceLanguage', selectedOption.code)
  
  // 显示选择结果提示
  uni.showToast({
    title: `已选择：${selectedOption.name}`,
    icon: 'none',
    duration: 1500
  })
}

const onTargetLanguageChange = (e: any) => {
  const selectedOption = targetLanguageOptions[e.detail.value]
  emit('update:targetLanguage', selectedOption.code)
  
  // 显示选择结果提示
  uni.showToast({
    title: `已选择：${selectedOption.name}`,
    icon: 'none',
    duration: 1500
  })
}

</script>

<style scoped>
.language-selector {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.selector-label {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 0 4rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.label-hint {
  font-size: 22rpx;
  color: #6b7280;
  line-height: 1.4;
}

.selector-container {
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.selector-container:active {
  border-color: #6366f1;
  box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-1rpx);
}

.selector-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
}

.selector-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.selector-icon {
  font-size: 24rpx;
  opacity: 0.8;
}

.selector-text {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  flex: 1;
}

.dropdown-icon {
  font-size: 20rpx;
  color: #9ca3af;
  transition: all 0.2s ease;
  line-height: 1;
}

.selector-container:active .dropdown-icon {
  transform: rotate(180deg);
  color: #6366f1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .selector-display {
    padding: 18rpx 20rpx;
  }
  
  .selector-text {
    font-size: 26rpx;
  }
  
  .label-text {
    font-size: 26rpx;
  }
  
  .label-hint {
    font-size: 20rpx;
  }
}
</style>
