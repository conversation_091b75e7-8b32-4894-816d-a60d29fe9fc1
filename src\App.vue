<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

onLaunch(() => {
  // 设置状态栏样式 - 添加错误处理
  try {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#4f46e5',
      fail: (err) => {
        // 静默处理失败，不影响应用正常运行
      }
    });
  } catch (error) {
    // 静默处理错误，不影响应用正常运行
  }

  // 初始化全局配置
  initGlobalConfig();
});

onShow(() => {
  // App显示时的处理
});

onHide(() => {
  // App隐藏时的处理
});

// 初始化全局配置
const initGlobalConfig = () => {
  try {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();

    // 在微信小程序环境中，不需要手动设置CSS变量
    // 系统会自动处理安全区域

  } catch (error) {
    // 静默处理错误
  }
};
</script>

<style>
/* 全局基础样式 - 兼容微信小程序 */
page {
  background-color: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

/* 全局容器样式 */
view {
  box-sizing: border-box;
}

/* 全局输入框样式 */
input, textarea {
  box-sizing: border-box;
  border: none;
  outline: none;
  background: transparent;
}

/* 全局按钮样式 */
button {
  box-sizing: border-box;
  border: none;
  outline: none;
  background: transparent;
}

/* 全局图片样式 */
image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 全局文字样式 */
text {
  word-wrap: break-word;
  word-break: break-all;
}

/* 全局动画性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 全局触摸反馈 */
.touch-feedback {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* 全局加载状态 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.global-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
