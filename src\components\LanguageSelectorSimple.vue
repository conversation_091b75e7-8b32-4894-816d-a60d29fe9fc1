<template>
  <view class="language-selector">
    <!-- 源语言选择器 -->
    <view class="selector-group">
      <view class="selector-label">
        <text class="label-text">源语言</text>
        <text class="label-hint">选择源语言可提高识别准确性</text>
      </view>
      <view class="selector-container" @click="showSourcePicker">
        <view class="selector-display">
          <text class="selector-text">{{ sourceLanguageDisplay }}</text>
          <text class="selector-icon">▼</text>
        </view>
      </view>
    </view>

    <!-- 目标语言选择器 -->
    <view class="selector-group">
      <view class="selector-label">
        <text class="label-text">目标语言</text>
      </view>
      <view class="selector-container" @click="showTargetPicker">
        <view class="selector-display">
          <text class="selector-text">{{ targetLanguageDisplay }}</text>
          <text class="selector-icon">▼</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LanguageSelectorSimple',
  props: {
    sourceLanguage: {
      type: String,
      default: 'auto'
    },
    targetLanguage: {
      type: String,
      default: 'zh'
    }
  },
  emits: ['update:sourceLanguage', 'update:targetLanguage'],
  data() {
    return {
      sourceLanguageOptions: [
        { code: 'auto', name: '自动识别' },
        { code: 'en', name: '英语' },
        { code: 'ja', name: '日语' },
        { code: 'ko', name: '韩语' },
        { code: 'de', name: '德语' },
        { code: 'fr', name: '法语' },
        { code: 'ru', name: '俄语' },
        { code: 'zh', name: '中文' }
      ],
      targetLanguageOptions: [
        { code: 'zh', name: '中文' },
        { code: 'en', name: '英语' },
        { code: 'ja', name: '日语' },
        { code: 'ko', name: '韩语' },
        { code: 'de', name: '德语' },
        { code: 'fr', name: '法语' },
        { code: 'ru', name: '俄语' }
      ]
    }
  },
  computed: {
    sourceLanguageDisplay() {
      const option = this.sourceLanguageOptions.find(option => option.code === this.sourceLanguage)
      return option?.name || '自动识别'
    },
    targetLanguageDisplay() {
      const option = this.targetLanguageOptions.find(option => option.code === this.targetLanguage)
      return option?.name || '中文'
    }
  },
  methods: {
    showSourcePicker() {
      uni.showActionSheet({
        itemList: this.sourceLanguageOptions.map(option => option.name),
        success: (res) => {
          const selectedOption = this.sourceLanguageOptions[res.tapIndex]
          this.$emit('update:sourceLanguage', selectedOption.code)
        }
      })
    },
    showTargetPicker() {
      uni.showActionSheet({
        itemList: this.targetLanguageOptions.map(option => option.name),
        success: (res) => {
          const selectedOption = this.targetLanguageOptions[res.tapIndex]
          this.$emit('update:targetLanguage', selectedOption.code)
        }
      })
    }
  }
}
</script>

<style scoped>
.language-selector {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.selector-label {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.label-hint {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.selector-container {
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.3s ease;
}

.selector-container:active {
  border-color: #6366f1;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.15);
}

.selector-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
}

.selector-text {
  font-size: 30rpx;
  color: #1f2937;
  font-weight: 500;
}

.selector-icon {
  font-size: 24rpx;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.selector-container:active .selector-icon {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .selector-display {
    padding: 20rpx 24rpx;
  }
  
  .selector-text {
    font-size: 28rpx;
  }
}
</style>
