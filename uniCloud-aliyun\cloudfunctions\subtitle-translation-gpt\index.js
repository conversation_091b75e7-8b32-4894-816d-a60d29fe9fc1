// uniCloud云函数：基于GPT的高质量字幕翻译
"use strict";

const createConfig = require("uni-config-center");

// 创建配置实例
const languageConfig = createConfig({ pluginId: "subtitle-language" });
const gptConfig = createConfig({
  pluginId: "openai-api",
  defaultConfig: {
    baseUrl: "https://aihubmix.com",
    model: "gpt-5-mini",
  },
});
const aliyunConfig = createConfig({
  pluginId: "aliyun-oss",
  defaultConfig: {
    region: "oss-cn-shanghai",
    bucket: "video--tanslate",
  },
});

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-5-mini",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000,
  API_TIMEOUT: 60000 * 2,
  BATCH_SIZE: 50,
  MAX_RETRIES: 3,
  MAX_CONCURRENT_BATCHES: 10,
};



/**
 * 标准化语言标识符
 * @param {string} languageIdentifier - 原始语言标识符
 * @returns {string} 标准化后的语言代码
 */
function normalizeLanguageIdentifier(languageIdentifier) {
  if (!languageIdentifier) return "en";

  const languageMap = languageConfig.config("languageMap") || {};
  const identifierMap = languageConfig.config("languageIdentifierMap") || {};

  if (languageMap[languageIdentifier]) {
    return languageIdentifier;
  }

  const normalizedCode = identifierMap[languageIdentifier];
  if (normalizedCode) {
    return normalizedCode;
  }

  const lowerCase = languageIdentifier.toLowerCase();
  const lowerCaseMatch = identifierMap[lowerCase];
  if (lowerCaseMatch) {
    return lowerCaseMatch;
  }

  console.warn(`未知的语言标识符: ${languageIdentifier}，使用默认值 en`);
  return "en";
}

/**
 * 并发翻译字幕条目 - 使用Promise.all
 */
async function translateSubtitlesBatchOptimized(
  entries,
  apiKey,
  baseUrl,
  model,
  sourceLanguage,
  targetLanguage
) {
  const translationStartTime = Date.now();
  console.log("开始并发翻译字幕", {
    totalEntries: entries.length,
    batchSize: CONFIG.BATCH_SIZE,
    maxConcurrent: CONFIG.MAX_CONCURRENT_BATCHES,
    sourceLanguage,
    targetLanguage,
    model,
  });

  // 过滤有效文本条目
  const validEntries = [];
  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
    }
  });

  if (validEntries.length === 0) {
    console.log("没有有效的字幕文本需要翻译");
    return entries;
  }

  console.log(`有效字幕条目: ${validEntries.length}/${entries.length}`);

  // 将有效条目分批处理
  const batches = [];
  for (let i = 0; i < validEntries.length; i += CONFIG.BATCH_SIZE) {
    batches.push(validEntries.slice(i, i + CONFIG.BATCH_SIZE));
  }

  console.log(`分为 ${batches.length} 批处理，每批最多 ${CONFIG.BATCH_SIZE} 条`);

  // 复制原数组用于存储翻译结果
  const translatedEntries = [...entries];

  // 纯并发处理
  console.log(`启用纯并发处理，最大并发数: ${CONFIG.MAX_CONCURRENT_BATCHES}`);
  const totalTranslatedCount = await processBatchesConcurrently(
    batches,
    translatedEntries,
    apiKey,
    baseUrl,
    model,
    targetLanguage
  );

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`并发翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedCount: totalTranslatedCount,
    batchCount: batches.length,
    processingTime: `${totalTime.toFixed(2)}秒`,
    successRate: `${((totalTranslatedCount / validEntries.length) * 100).toFixed(1)}%`,
  });

  return translatedEntries;
}

/**
 * 并发处理多个批次 - 使用Promise.all
 */
async function processBatchesConcurrently(
  batches,
  translatedEntries,
  apiKey,
  baseUrl,
  model,
  targetLanguage
) {
  let totalTranslatedCount = 0;
  const maxConcurrent = Math.min(CONFIG.MAX_CONCURRENT_BATCHES, batches.length);

  // 将批次分组，每组最多包含 maxConcurrent 个批次
  for (let i = 0; i < batches.length; i += maxConcurrent) {
    const batchGroup = batches.slice(i, i + maxConcurrent);
    const groupStartTime = Date.now();

    console.log(
      `并发处理第 ${i + 1}-${Math.min(i + maxConcurrent, batches.length)} 批次（共 ${
        batchGroup.length
      } 个并发）`
    );


    const concurrentTasks = batchGroup.map(async (batch, groupIndex) => {
      const actualBatchIndex = i + groupIndex + 1;
      try {
        const batchResult = await translateBatchWithRetry(
          batch,
          apiKey,
          baseUrl,
          model,
          targetLanguage,
          actualBatchIndex
        );
        return { success: true, batchResult, batch, batchIndex: actualBatchIndex };
      } catch (error) {
        return { success: false, error, batch, batchIndex: actualBatchIndex };
      }
    });

    // 等待当前组的所有批次完成
    const results = await Promise.all(concurrentTasks);

    // 处理结果
    results.forEach(({ success, batchResult, batch, batc  hIndex, error }) => {
      if (success) {
        // 将翻译结果合并到最终数组中
        batchResult.forEach((translatedEntry) => {
          if (translatedEntry.originalIndex !== undefined) {
            translatedEntries[translatedEntry.originalIndex] = translatedEntry;
            totalTranslatedCount++;
          }
        });
        console.log(`第 ${batchIndex} 批翻译完成`);
      } else {
        console.error(`第 ${batchIndex} 批翻译失败:`, error.message);
        // 批次失败时保留原文
        batch.forEach((entry) => {
          if (entry.originalIndex !== undefined) {
            translatedEntries[entry.originalIndex] = entry;
          }
        });
      }
    });

    const groupTime = (Date.now() - groupStartTime) / 1000;
    console.log(`并发组处理完成，耗时: ${groupTime.toFixed(2)}秒`);
  }

  return totalTranslatedCount;
}

/**
 * 带重试机制的单批次翻译函数
 */
async function translateBatchWithRetry(batch, apiKey, baseUrl, model, targetLanguage, batchNumber) {
  let lastError = null;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试翻译...`);

      const result = await translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage);

      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试成功`);
      return result;
    } catch (error) {
      lastError = error;
      console.error(`第 ${batchNumber} 批第 ${attempt} 次尝试失败:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        console.log(`立即重试第 ${attempt + 1} 次...`);
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw new Error(
    `第 ${batchNumber} 批翻译失败，已重试 ${CONFIG.MAX_RETRIES} 次: ${lastError.message}`
  );
}

/**
 * 翻译单个批次的字幕条目
 */
async function translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage) {
  // 构建批次文本
  const batchTexts = batch.map((entry, index) => `${index + 1}. ${entry.text.trim()}`);
  const combinedText = batchTexts.join("\n");

  console.log(`批次文本长度: ${combinedText.length} 字符，包含 ${batch.length} 条字幕`);

  // 获取目标语言名称
  const languageMap = languageConfig.config("languageMap") || {};
  const targetLangName = languageMap[targetLanguage] || targetLanguage.toUpperCase();

  // 构建翻译请求
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位资深的多语言翻译专家，将带编号文本翻译成地道流畅的${targetLangName}。要求：保持编号格式，逐行对应翻译，忠实原意，自然表达。仅输出译文。`,
      },
      {
        role: "user",
        content: `翻译成${targetLangName}：\n\n${combinedText}`,
      },
    ],
    temperature: CONFIG.TEMPERATURE,
    max_completion_tokens: CONFIG.MAX_TOKENS,
  };

  // 调用GPT API
  const apiStartTime = Date.now();
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`API响应耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    let errorMessage = `GPT API请求失败，状态码: ${response.status}`;
    if (response.data?.error?.message) {
      errorMessage += ` - ${response.data.error.message}`;
    }
    throw new Error(errorMessage);
  }

  const result = response.data;
  if (!result.choices?.length || !result.choices[0].message?.content) {
    throw new Error("GPT API返回空结果或格式错误");
  }

  const translatedText = result.choices[0].message.content.trim();
  if (!translatedText) {
    throw new Error("GPT翻译返回空白内容");
  }

  // 解析翻译结果
  const translatedLines = translatedText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line);

  console.log(`解析翻译结果，共 ${translatedLines.length} 行`);

  // 匹配翻译结果到原始条目
  const translatedBatch = [...batch];
  batch.forEach((entry, index) => {
    const targetPattern = `${index + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      translatedBatch[index] = {
        ...entry,
        text: translatedLine,
      };
    }

  });

  return translatedBatch;
}

// 辅助函数
function createSuccessResponse(message, data) {
  return { code: 200, message, data, timestamp: new Date().toISOString() };
}

function createErrorResponse(code, message, extra = {}) {
  return { code, message, timestamp: new Date().toISOString(), ...extra };
}

async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data?.length) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 标准化语言标识符
  const rawSourceLanguage =
    sourceLanguage === "auto" ? task.detectedLanguage || "en" : sourceLanguage;
  const actualSourceLanguage = normalizeLanguageIdentifier(rawSourceLanguage);

  console.log(`语言标识符标准化: ${rawSourceLanguage} -> ${actualSourceLanguage}`);
  return { task, actualSourceLanguage };
}

async function getAndValidateGptConfig() {
  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl };
}

async function downloadSrtFromOSS(ossUrl) {
  if (!ossUrl || typeof ossUrl !== "string") {
    throw new Error(`无效的OSS URL: ${ossUrl}`);
  }

  const response = await uniCloud.httpclient.request(ossUrl, {
    method: "GET",
    timeout: 30000,
  });

  if (response.status !== 200) {
    throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
  }

  let srtContent = response.data;
  if (Buffer.isBuffer(srtContent)) {
    srtContent = srtContent.toString("utf8");
  } else if (typeof srtContent !== "string") {
    srtContent = String(srtContent || "");
  }

  if (!srtContent.trim()) {
    throw new Error("下载的SRT文件内容为空");
  }

  return srtContent;
}

function parseSRT(srtContent) {
  if (!srtContent || !srtContent.trim()) {
    throw new Error("SRT字幕内容为空");
  }

  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    if (!block?.trim()) continue;

    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n").trim();

      if (!isNaN(index) && timeRange && text) {
        entries.push({ index, timeRange, text });
      }
    }
  }

  console.log(`SRT解析完成，共 ${entries.length} 条有效字幕`);
  return entries;
}

/**
 * 生成ASS字幕格式
 * @param {Array} entries - 字幕条目数组
 * @param {string} targetLanguage - 目标语言
 * @param {Object} styleOverrides - 样式覆盖配置
 * @param {Object} videoResolution - 视频分辨率信息
 * @returns {string} ASS格式字符串
 */
function generateASS(entries, targetLanguage = "zh", styleOverrides = {}, videoResolution = null) {
  const encoding = getLanguageEncoding(targetLanguage);
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const baseStyle = assStyleConfig.baseStyle || {};
  const scriptInfo = assStyleConfig.scriptInfo || {};
  const languageWrapStyles = assStyleConfig.languageWrapStyles || {};
  const wrapStyle = languageWrapStyles[targetLanguage] || languageWrapStyles.default || scriptInfo.wrapStyle || 1;
  const style = { ...baseStyle, ...styleOverrides };

  let resolutionConfig = "";
  if (videoResolution && videoResolution.width && videoResolution.height) {
    resolutionConfig = `PlayResX: ${videoResolution.width}\nPlayResY: ${videoResolution.height}`;
  } else if (!scriptInfo.autoAdaptive) {
    resolutionConfig = `PlayResX: 1920\nPlayResY: 1080`;
  }
 
  const assHeader = `[Script Info]
Title: ${scriptInfo.title}
ScriptType: ${scriptInfo.scriptType}
WrapStyle: ${wrapStyle}
ScaledBorderAndShadow: ${scriptInfo.scaledBorderAndShadow}
${resolutionConfig}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${style.fontName},${style.fontSize},${style.primaryColor},${style.secondaryColor},${style.outlineColor},${style.backColor},${style.bold},${style.italic},${style.underline},${style.strikeOut},${style.scaleX},${style.scaleY},${style.spacing},${style.angle},${style.borderStyle},${style.outline},${style.shadow},${style.alignment},${style.marginL},${style.marginR},${style.marginV},${encoding}`;

  if (videoResolution && styleOverrides) {
    logSubtitleWrapStatistics(entries, targetLanguage, videoResolution, styleOverrides);
  }

  const assEvents = entries
    .map((entry, index) => {
      const { startTime, endTime } = convertTimeRange(entry.timeRange);
      const processedText = smartTextWrap(entry.text, targetLanguage, videoResolution, styleOverrides);
      return `Dialogue: ${index},${startTime},${endTime},Default,,0,0,0,,${processedText}`;
    })
    .join("\n");

  return `${assHeader}\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n${assEvents}`;
}

/**
 * 转换时间格式
 * @param {string} timeRange - SRT格式的时间范围
 * @returns {Object} ASS格式的开始和结束时间
 */
function convertTimeRange(timeRange) {
  const [start, end] = timeRange.split(" --> ");
  return {
    startTime: convertSRTTimeToASS(start),
    endTime: convertSRTTimeToASS(end),
  };
}

/**
 * 将SRT时间格式转换为ASS时间格式
 * @param {string} srtTime - SRT时间格式 (HH:MM:SS,mmm)
 * @returns {string} ASS时间格式 (H:MM:SS.cc)
 */
function convertSRTTimeToASS(srtTime) {
  const [time, milliseconds] = srtTime.split(",");
  const centiseconds = Math.floor(parseInt(milliseconds) / 10);
  const [hours, minutes, seconds] = time.split(":");
  return `${parseInt(hours)}:${minutes}:${seconds}.${centiseconds.toString().padStart(2, "0")}`;
}

/**
 * 获取语言编码
 * @param {string} targetLanguage - 目标语言代码
 * @returns {number} 对应的字符编码
 */
function getLanguageEncoding(targetLanguage) {
  const encodings = languageConfig.config("languageEncodings") || {};
  return encodings[targetLanguage] || 1;
}

/**
 * 获取语言特定字体
 * @param {string} targetLanguage - 目标语言代码
 * @returns {string} 对应的字体名称
 */
function getLanguageFont(targetLanguage) {
  const fonts = languageConfig.config("languageFonts") || { default: "Arial" };
  return fonts[targetLanguage] || fonts.default || "Arial";
}



/**
 * 检测字幕内容是否需要智能换行处理（主要针对中文）
 * @param {string} text - 字幕文本内容
 * @param {string} targetLanguage - 目标语言代码
 * @returns {boolean} 是否需要智能换行处理
 */
function isChineseContent(text, targetLanguage) {
  // 从配置获取需要智能换行的语言列表
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const intelligentWrapLanguages = wrapConfig.intelligentWrapLanguages || ['zh', 'zh-cn', 'zh-tw'];

  // 首先检查目标语言是否在智能换行语言列表中
  const isIntelligentWrapLanguage = intelligentWrapLanguages.includes(targetLanguage.toLowerCase());

  // 如果目标语言在智能换行列表中，直接返回true
  if (isIntelligentWrapLanguage) {
    return true;
  }

  // 如果目标语言不在列表中，检测文本内容中的中文字符比例
  if (!text || !text.trim()) {
    return false;
  }

  // 统计中文字符数量
  const chineseCharRegex = /[\u4e00-\u9fff\u3400-\u4dbf]/g;
  const chineseMatches = text.match(chineseCharRegex);
  const chineseCharCount = chineseMatches ? chineseMatches.length : 0;


  const totalCharCount = text.replace(/[\s\p{P}]/gu, '').length;

  if (totalCharCount === 0) {
    return false;
  }

  // 如果中文字符占比超过30%，认为是中文内容，需要智能换行
  const chineseRatio = chineseCharCount / totalCharCount;
  return chineseRatio > 0.3;
}

/**
 * 智能文本换行处理
 * @param {string} text - 原始文本
 * @param {string} targetLanguage - 目标语言
 * @param {Object} videoResolution - 视频分辨率信息
 * @param {Object} styleConfig - 字幕样式配置
 * @returns {string} 处理后的文本
 */
function smartTextWrap(text, targetLanguage = "zh", videoResolution = null, styleConfig = null) {
  if (!text || !text.trim()) return text;

  const needsIntelligentWrap = isChineseContent(text, targetLanguage);

  if (needsIntelligentWrap) {
    let processedText = text.replace(/\r?\n/g, " ").trim();

    if (!videoResolution || !styleConfig) {
      return processedText;
    }

    const availableWidth = calculateAvailableTextWidth(videoResolution, styleConfig);
    return performIntelligentWrap(processedText, targetLanguage, availableWidth, styleConfig);
  } else {
    return text.replace(/\r?\n/g, "\\N");
  }
}

/**
 * 计算可用的文本显示宽度
 * @param {Object} videoResolution - 视频分辨率 {width, height}
 * @param {Object} styleConfig - 字幕样式配置 {fontSize, marginL, marginR}
 * @returns {number} 可用宽度（像素）
 */
function calculateAvailableTextWidth(videoResolution, styleConfig) {
  const { width } = videoResolution;
  const { marginL = 0, marginR = 0 } = styleConfig;
  const availableWidth = width - marginL - marginR;
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const maxLineSettings = wrapConfig.maxLineSettings || {};
  const safeWidthRatio = maxLineSettings.safeWidthRatio || 0.95;

  return Math.floor(availableWidth * safeWidthRatio);
}

/**
 * 执行智能换行处理
 * @param {string} text - 文本内容
 * @param {string} targetLanguage - 目标语言
 * @param {number} availableWidth - 可用宽度（像素）
 * @param {Object} styleConfig - 字幕样式配置
 * @returns {string} 换行后的文本
 */
function performIntelligentWrap(text, targetLanguage, availableWidth, styleConfig) {
  const { fontSize } = styleConfig;
  const avgCharWidth = estimateCharacterWidth(targetLanguage, fontSize);
  const maxCharsPerLine = Math.floor(availableWidth / avgCharWidth);
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const maxLineSettings = wrapConfig.maxLineSettings || {};
  const wrapThreshold = maxLineSettings.wrapThreshold || 1.15;
  const actualWrapThreshold = maxCharsPerLine * wrapThreshold;

  if (getTextDisplayLength(text, targetLanguage) <= actualWrapThreshold) {
    return text;
  }

  return wrapTextByLanguage(text, targetLanguage, maxCharsPerLine);
}

/**
 * 估算字符宽度
 * @param {string} targetLanguage - 目标语言
 * @param {number} fontSize - 字体大小
 * @returns {number} 平均字符宽度（像素）
 */
function estimateCharacterWidth(targetLanguage, fontSize) {
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const characterWidthFactors = wrapConfig.characterWidthFactors || {
    'zh': 1.0, 'zh-cn': 1.0, 'zh-tw': 1.0, 'ja': 1.0, 'ko': 1.0,
    'en': 0.6, 'fr': 0.6, 'de': 0.6, 'es': 0.6, 'it': 0.6, 'pt': 0.6,
    'ru': 0.7, 'ar': 0.8, 'hi': 0.8, 'th': 0.8, 'vi': 0.6, 'default': 0.7
  };

  const widthFactor = characterWidthFactors[targetLanguage] || characterWidthFactors.default;
  return Math.ceil(fontSize * widthFactor);
}

/**
 * 计算文本显示长度（考虑不同字符的显示宽度）
 * @param {string} text - 文本内容
 * @param {string} targetLanguage - 目标语言
 * @returns {number} 显示长度
 */
function getTextDisplayLength(text, targetLanguage) {
  if (!text) return 0;

  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const cjkLanguages = wrapConfig.cjkLanguages || ['zh', 'zh-cn', 'zh-tw', 'ja', 'ko'];
  const characterWidthFactors = wrapConfig.characterWidthFactors || {};
  const isCJK = cjkLanguages.includes(targetLanguage);
  const nonCJKFactor = characterWidthFactors[targetLanguage] || characterWidthFactors.default || 0.7;

  if (isCJK) {
    let length = 0;
    for (const char of text) {
      if (/[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/.test(char)) {
        length += 1;
      } else {
        length += nonCJKFactor;
      }
    }
    return length;
  } else {
    return text.length * nonCJKFactor;
  }
}

/**
 * 根据语言特性进行文本换行
 * @param {string} text - 文本内容
 * @param {string} targetLanguage - 目标语言
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @returns {string} 换行后的文本
 */
function wrapTextByLanguage(text, targetLanguage, maxCharsPerLine) {
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const cjkLanguages = wrapConfig.cjkLanguages || ['zh', 'zh-cn', 'zh-tw', 'ja', 'ko'];
  const isCJK = cjkLanguages.includes(targetLanguage);

  if (isCJK) {
    return wrapCJKText(text, maxCharsPerLine, targetLanguage);
  } else {
    return wrapWesternText(text, maxCharsPerLine, targetLanguage);
  }
}

/**
 * 中日韩文本换行处理
 * @param {string} text - 文本内容
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @param {string} targetLanguage - 目标语言
 * @returns {string} 换行后的文本
 */
function wrapCJKText(text, maxCharsPerLine, targetLanguage) {
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const breakRules = wrapConfig.breakRules?.cjk || {
    canBreakAnywhere: true,
    avoidBreakAfter: ["（", "【", "《", "\"", "'"],
    avoidBreakBefore: ["）", "】", "》", "'", "'", "，", "。", "！", "？", "；", "："]
  };
  const maxLineSettings = wrapConfig.maxLineSettings || { preferredMaxLines: 2, absoluteMaxLines: 3 };

  const lines = [];
  let currentLine = '';
  let currentLength = 0;

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charWidth = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/.test(char) ? 1 : 0.6;
    const wouldExceedLimit = currentLength + charWidth > maxCharsPerLine;
    const shouldBreak = wouldExceedLimit && currentLine.length > 0;

    if (shouldBreak) {
      const canBreakHere = canBreakAtPosition(text, i, breakRules);

      if (canBreakHere || currentLength > maxCharsPerLine * 1.2) {
        lines.push(currentLine.trim());
        currentLine = char;
        currentLength = charWidth;
      } else {
        currentLine += char;
        currentLength += charWidth;
      }
    } else {
      currentLine += char;
      currentLength += charWidth;
    }
  }

  if (currentLine.trim()) {
    lines.push(currentLine.trim());
  }

  if (lines.length > maxLineSettings.preferredMaxLines) {
    return redistributeCJKLines(lines, maxCharsPerLine, maxLineSettings);
  }

  return lines.join('\\N');
}

/**
 * 检查是否可以在指定位置换行
 * @param {string} text - 完整文本
 * @param {number} position - 当前位置
 * @param {Object} breakRules - 换行规则
 * @returns {boolean} 是否可以换行
 */
function canBreakAtPosition(text, position, breakRules) {
  if (!breakRules.canBreakAnywhere) return false;

  const currentChar = text[position];
  const prevChar = position > 0 ? text[position - 1] : '';

  // 检查是否在避免换行的字符之后
  if (breakRules.avoidBreakAfter && breakRules.avoidBreakAfter.includes(prevChar)) {
    return false;
  }

  // 检查是否在避免换行的字符之前
  if (breakRules.avoidBreakBefore && breakRules.avoidBreakBefore.includes(currentChar)) {
    return false;
  }

  return true;
}

/**
 * 重新分配CJK文本行，优化行数
 * @param {Array} lines - 原始行数组
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @param {Object} maxLineSettings - 最大行数设置
 * @returns {string} 重新分配后的文本
 */
function redistributeCJKLines(lines, maxCharsPerLine, maxLineSettings) {
  const { preferredMaxLines, absoluteMaxLines } = maxLineSettings;

  if (lines.length <= absoluteMaxLines) {
    return lines.join('\\N');
  }

  // 如果行数超过绝对最大值，合并一些行
  const redistributedLines = [];
  let currentLine = '';

  for (const line of lines) {
    const testLine = currentLine ? `${currentLine} ${line}` : line;

    if (getTextDisplayLength(testLine, 'zh') <= maxCharsPerLine * 1.1) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        redistributedLines.push(currentLine);
      }
      currentLine = line;
    }

    // 如果已经达到首选行数，停止
    if (redistributedLines.length >= preferredMaxLines - 1) {
      break;
    }
  }

  if (currentLine) {
    redistributedLines.push(currentLine);
  }

  return redistributedLines.join('\\N');
}

/**
 * 西文文本换行处理（在单词边界换行）
 * @param {string} text - 文本内容
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @param {string} targetLanguage - 目标语言
 * @returns {string} 换行后的文本
 */
function wrapWesternText(text, maxCharsPerLine, targetLanguage) {
  // 从配置获取换行规则和字符宽度系数
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const breakRules = wrapConfig.breakRules?.western || {
    canBreakAnywhere: false,
    breakOnWhitespace: true,
    breakOnHyphen: true,
    avoidBreakAfter: ["(", "[", "{", "\"", "'"],
    avoidBreakBefore: [")", "]", "}", "\"", "'", ",", ".", "!", "?", ";", ":"]
  };
  const characterWidthFactors = wrapConfig.characterWidthFactors || {};
  const charFactor = characterWidthFactors[targetLanguage] || characterWidthFactors.default || 0.7;
  const maxLineSettings = wrapConfig.maxLineSettings || { preferredMaxLines: 2, absoluteMaxLines: 3 };

  const words = text.split(/\s+/);
  const lines = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;
    const testLength = testLine.length * charFactor;

    if (testLength <= maxCharsPerLine) {
      currentLine = testLine;
    } else {
      // 如果当前行不为空，先保存当前行
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        // 如果单个单词就超长，检查是否可以在连字符处分割
        if (word.length * charFactor > maxCharsPerLine) {
          const splitWord = splitLongWord(word, maxCharsPerLine, charFactor, breakRules);
          lines.push(...splitWord.slice(0, -1));
          currentLine = splitWord[splitWord.length - 1] || '';
        } else {
          currentLine = word;
        }
      }
    }
  }

  // 添加最后一行
  if (currentLine) {
    lines.push(currentLine);
  }

  // 如果行数过多，尝试重新分配
  if (lines.length > maxLineSettings.preferredMaxLines) {
    return redistributeWesternLines(lines, maxCharsPerLine, charFactor, maxLineSettings);
  }

  // 使用 ASS 格式的换行符连接
  return lines.join('\\N');
}

/**
 * 分割过长的单词
 * @param {string} word - 单词
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @param {number} charFactor - 字符宽度系数
 * @param {Object} breakRules - 换行规则
 * @returns {Array} 分割后的单词片段数组
 */
function splitLongWord(word, maxCharsPerLine, charFactor, breakRules) {
  const maxWordChars = Math.floor(maxCharsPerLine / charFactor);
  const parts = [];

  // 首先尝试在连字符处分割
  if (breakRules.breakOnHyphen && word.includes('-')) {
    const hyphenParts = word.split('-');
    let currentPart = '';

    for (let i = 0; i < hyphenParts.length; i++) {
      const part = hyphenParts[i] + (i < hyphenParts.length - 1 ? '-' : '');
      const testPart = currentPart ? `${currentPart}${part}` : part;

      if (testPart.length <= maxWordChars) {
        currentPart = testPart;
      } else {
        if (currentPart) {
          parts.push(currentPart);
        }
        currentPart = part;
      }
    }

    if (currentPart) {
      parts.push(currentPart);
    }

    return parts;
  }

  // 如果没有连字符或连字符分割后仍然过长，强制分割
  for (let i = 0; i < word.length; i += maxWordChars) {
    parts.push(word.slice(i, i + maxWordChars));
  }

  return parts;
}

/**
 * 重新分配西文文本行，优化行数
 * @param {Array} lines - 原始行数组
 * @param {number} maxCharsPerLine - 每行最大字符数
 * @param {number} charFactor - 字符宽度系数
 * @param {Object} maxLineSettings - 最大行数设置
 * @returns {string} 重新分配后的文本
 */
function redistributeWesternLines(lines, maxCharsPerLine, charFactor, maxLineSettings) {
  const { preferredMaxLines, absoluteMaxLines } = maxLineSettings;

  if (lines.length <= absoluteMaxLines) {
    return lines.join('\\N');
  }

  // 如果行数超过绝对最大值，合并一些行
  const redistributedLines = [];
  let currentLine = '';

  for (const line of lines) {
    const testLine = currentLine ? `${currentLine} ${line}` : line;

    if (testLine.length * charFactor <= maxCharsPerLine * 1.1) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        redistributedLines.push(currentLine);
      }
      currentLine = line;
    }

    // 如果已经达到首选行数，停止
    if (redistributedLines.length >= preferredMaxLines - 1) {
      break;
    }
  }

  if (currentLine) {
    redistributedLines.push(currentLine);
  }

  return redistributedLines.join('\\N');
}

/**
 * 记录字幕换行处理的统计信息
 * @param {Array} entries - 字幕条目数组
 * @param {string} targetLanguage - 目标语言
 * @param {Object} videoResolution - 视频分辨率
 * @param {Object} styleConfig - 样式配置
 */
function logSubtitleWrapStatistics(entries, targetLanguage, videoResolution, styleConfig) {
  if (!entries || entries.length === 0) return;

  let wrappedEntries = 0;
  entries.forEach((entry) => {
    if (!entry.text) return;
    const processedText = smartTextWrap(entry.text, targetLanguage, videoResolution, styleConfig);
    if (processedText.includes('\\N')) {
      wrappedEntries++;
    }
  });

  console.log(`字幕换行统计: 总计${entries.length}条, 换行${wrappedEntries}条, 语言${targetLanguage}`);
}

/**
 * 获取视频分辨率信息（从数据库读取或使用默认值）
 * @param {string} taskId - 任务ID（用于查询视频信息）
 * @param {Object} tasksCollection - 数据库任务集合
 * @returns {Promise<Object>} 包含视频宽度和高度的对象
 */
async function getVideoResolution(taskId, tasksCollection) {
  try {
    if (taskId && tasksCollection) {
      const taskResult = await tasksCollection.doc(taskId).get();
      if (taskResult.data && taskResult.data.length > 0) {
        const task = taskResult.data[0];
        if (task.videoWidth && task.videoHeight) {
          console.log(`从数据库获取视频分辨率: ${task.videoWidth}x${task.videoHeight}`);
          return {
            width: task.videoWidth,
            height: task.videoHeight,
            source: "database",
          };
        }
      }
    }
  } catch (error) {
    console.warn("从数据库获取视频分辨率失败:", error.message);
  }

  console.log("使用默认视频分辨率: 1920x1080");
  return {
    width: 1920,
    height: 1080,
    source: "default",
  };
}

/**
 * 视频分辨率分析器
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {Object} 分辨率分析结果
 */
function analyzeVideoResolution(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) {
    console.log(`⚠️  视频分辨率信息缺失，使用默认分析结果`);
    return {
      width: 1920,
      height: 1080,
      aspectRatio: 16/9,
      orientation: "landscape",
      category: "fhd",
      pixelCount: 1920 * 1080,
      isStandard: true,
      displayName: "1080p (默认)"
    };
  }

  const aspectRatio = videoWidth / videoHeight;
  const pixelCount = videoWidth * videoHeight;

  // 确定视频方向
  let orientation;
  if (aspectRatio > 1.1) {
    orientation = "landscape"; // 横屏
  } else if (aspectRatio < 0.9) {
    orientation = "portrait";  // 竖屏
  } else {
    orientation = "square";    // 正方形
  }

  // 基于像素总数和常见分辨率进行精确分类
  let category, displayName, isStandard = false;


  const standardResolutions = [
    { width: 640, height: 480, name: "480p", category: "sd" },
    { width: 854, height: 480, name: "480p Wide", category: "sd" },
    { width: 1280, height: 720, name: "720p", category: "hd" },
    { width: 1920, height: 1080, name: "1080p", category: "fhd" },
    { width: 2560, height: 1440, name: "1440p", category: "qhd" },
    { width: 3840, height: 2160, name: "4K", category: "uhd" },
    { width: 7680, height: 4320, name: "8K", category: "uhd8k" },
    // 竖屏标准分辨率
    { width: 720, height: 1280, name: "720p 竖屏", category: "hd" },
    { width: 1080, height: 1920, name: "1080p 竖屏", category: "fhd" },
    { width: 1440, height: 2560, name: "1440p 竖屏", category: "qhd" },
    // 正方形分辨率
    { width: 720, height: 720, name: "720p 正方形", category: "hd" },
    { width: 1080, height: 1080, name: "1080p 正方形", category: "fhd" }
  ];

  // 检查是否匹配标准分辨率
  for (const std of standardResolutions) {
    const widthMatch = Math.abs(videoWidth - std.width) / std.width < 0.05;
    const heightMatch = Math.abs(videoHeight - std.height) / std.height < 0.05;
    if (widthMatch && heightMatch) {
      category = std.category;
      displayName = std.name;
      isStandard = true;
      break;
    }
  }

  // 如果不是标准分辨率，基于像素总数分类
  if (!isStandard) {
    if (pixelCount <= 500000) {
      category = "sd";
      displayName = `${videoWidth}x${videoHeight} (SD级别)`;
    } else if (pixelCount <= 1000000) {
      category = "hd";
      displayName = `${videoWidth}x${videoHeight} (HD级别)`;
    } else if (pixelCount <= 2500000) {
      category = "fhd";
      displayName = `${videoWidth}x${videoHeight} (FHD级别)`;
    } else if (pixelCount <= 4000000) {
      category = "qhd";
      displayName = `${videoWidth}x${videoHeight} (QHD级别)`;
    } else if (pixelCount <= 9000000) {
      category = "uhd";
      displayName = `${videoWidth}x${videoHeight} (4K级别)`;
    } else {
      category = "uhd8k";
      displayName = `${videoWidth}x${videoHeight} (8K级别)`;
    }
  }

  const result = {
    width: videoWidth,
    height: videoHeight,
    aspectRatio: Math.round(aspectRatio * 1000) / 1000,
    orientation,
    category,
    pixelCount,
    isStandard,
    displayName
  };

  console.log(`视频分辨率分析: ${displayName} (${videoWidth}×${videoHeight})`);

  return result;
}

/**
 * 字幕字体大小计算器
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @param {string} targetLanguage - 目标语言
 * @returns {Object} 字幕样式配置
 */
function calculateSubtitleStyle(videoWidth, videoHeight, targetLanguage) {
  const videoAnalysis = analyzeVideoResolution(videoWidth, videoHeight);

  const fontSizeStandards = {
    landscape: {
      sd: 0.045, hd: 0.042, fhd: 0.040, qhd: 0.038, uhd: 0.035, uhd8k: 0.032
    },
    portrait: {
      sd: 0.038, hd: 0.035, fhd: 0.032, qhd: 0.030, uhd: 0.028, uhd8k: 0.025
    },
    square: {
      sd: 0.042, hd: 0.039, fhd: 0.036, qhd: 0.034, uhd: 0.031, uhd8k: 0.028
    }
  };

  const marginStandards = {
    landscape: { bottom: 0.08, side: 0.05 },
    portrait: { bottom: 0.12, side: 0.08 },
    square: { bottom: 0.10, side: 0.06 }
  };

  const languageAdjustments = {
    zh: { fontScale: 1.0, marginScale: 1.1 },
    ja: { fontScale: 1.0, marginScale: 1.05 },
    ko: { fontScale: 1.0, marginScale: 1.02 },
    en: { fontScale: 0.95, marginScale: 1.0 },
    default: { fontScale: 1.0, marginScale: 1.0 }
  };

  const fontSizeRatio = fontSizeStandards[videoAnalysis.orientation][videoAnalysis.category];
  const baseFontSize = Math.round(videoAnalysis.height * fontSizeRatio);
  const langAdjust = languageAdjustments[targetLanguage] || languageAdjustments.default;
  const finalFontSize = Math.round(baseFontSize * langAdjust.fontScale);
  const marginStd = marginStandards[videoAnalysis.orientation];
  const bottomMargin = Math.round(videoAnalysis.height * marginStd.bottom * langAdjust.marginScale);
  const sideMargin = Math.round(videoAnalysis.width * marginStd.side);

  let aspectRatioAdjustment = 1.0;
  if (videoAnalysis.orientation === "landscape") {
    if (videoAnalysis.aspectRatio > 2.0) {
      aspectRatioAdjustment = 0.9;
    } else if (videoAnalysis.aspectRatio < 1.5) {
      aspectRatioAdjustment = 1.1;
    }
  }

  const adjustedFontSize = Math.round(finalFontSize * aspectRatioAdjustment);
  const minFontSize = Math.max(16, Math.round(videoAnalysis.height * 0.02));
  const maxFontSize = Math.round(videoAnalysis.height * 0.08);
  const safeFontSize = Math.max(minFontSize, Math.min(maxFontSize, adjustedFontSize));

  return {
    fontSize: safeFontSize,
    marginV: bottomMargin,
    marginL: sideMargin,
    marginR: sideMargin,
    videoAnalysis: videoAnalysis,
  };
}

/**
 * 字幕样式生成器
 * @param {string} targetLanguage - 目标语言代码
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {Object} 字幕样式配置
 */
function getLanguageSpecificStyle(targetLanguage, videoWidth = null, videoHeight = null) {
  const styleCalc = calculateSubtitleStyle(videoWidth, videoHeight, targetLanguage);
  const languageFont = getLanguageFont(targetLanguage);
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const baseStyle = assStyleConfig.baseStyle || {};

  return {
    ...baseStyle,
    fontName: languageFont,
    fontSize: styleCalc.fontSize,
    marginL: styleCalc.marginL,
    marginR: styleCalc.marginR,
    marginV: styleCalc.marginV,
  };
}

async function uploadTranslatedAssToOSS(taskId, assContent) {
  const OSS = require("ali-oss");

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const region = aliyunConfig.config("region");
  const bucketName = aliyunConfig.config("bucket");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云OSS配置缺失");
  }

  const client = new OSS({
    accessKeyId,
    accessKeySecret,
    bucket: bucketName,
    region,
  });

  const timestamp = Date.now();
  const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.ass`;

  const uploadResult = await client.put(objectKey, Buffer.from(assContent, "utf8"), {
    headers: {
      "Content-Type": "text/plain; charset=utf-8",
    },
  });

  return {
    subtitleOssUrl: uploadResult.url,
    objectKey,
  };
}

/**
 * 通用字幕处理函数
 */
async function processSubtitles(taskId, entries, normalizedTargetLanguage, tasksCollection) {
  console.log("生成并上传字幕文件...");

  // 获取视频分辨率信息
  const videoResolution = await getVideoResolution(taskId, tasksCollection);
  const languageSpecificStyle = getLanguageSpecificStyle(
    normalizedTargetLanguage,
    videoResolution.width,
    videoResolution.height
  );

  const assContent = generateASS(
    entries,
    normalizedTargetLanguage,
    languageSpecificStyle,
    videoResolution
  );
  const uploadResult = await uploadTranslatedAssToOSS(taskId, assContent);

  // 更新任务状态为merging，准备字幕烧录
  await tasksCollection.doc(taskId).update({
    status: "merging",
    subtitleOssUrl: uploadResult.subtitleOssUrl,
    translationStarted: false,
    updateTime: new Date(),
  });

  console.log("字幕处理完成，准备启动字幕烧录");

  // 直接启动字幕烧录
  try {
    const mergeResult = await uniCloud.callFunction({
      name: "process-video-task",
      data: { taskId, action: "merge_subtitle" },
    });
    console.log("字幕烧录启动结果：", mergeResult.result);
  } catch (error) {
    console.error("字幕烧录启动异常：", error.message);
  }

  return {
    subtitleOssUrl: uploadResult.subtitleOssUrl,
    translatedCount: entries.length,
  };
}

exports.main = async (event) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    // 标准化目标语言标识符
    const normalizedTargetLanguage = normalizeLanguageIdentifier(targetLanguage);

    console.log("subtitle-translation-gpt 云函数启动（纯并发处理版本）");
    console.log("输入参数：", { taskId, sourceLanguage, targetLanguage: normalizedTargetLanguage });
    if (targetLanguage !== normalizedTargetLanguage) {
      console.log(`目标语言标准化: ${targetLanguage} -> ${normalizedTargetLanguage}`);
    }

    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === normalizedTargetLanguage) {
      console.log(
        `源语言(${actualSourceLanguage})和目标语言(${normalizedTargetLanguage})相同，跳过翻译但继续处理字幕文件`
      );

      console.log("开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);
      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }
      console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

      const result = await processSubtitles(taskId, subtitleEntries, normalizedTargetLanguage, tasksCollection);

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`字幕处理任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("语言相同，字幕处理成功", {
        taskId,
        status: "completed",
        reason: "same_language",
        translatedCount: result.translatedCount,
        subtitleOssUrl: result.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage: normalizedTargetLanguage,
      });
    }

    console.log(`开始翻译流程：${actualSourceLanguage} -> ${normalizedTargetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl } = await getAndValidateGptConfig();
    const model = CONFIG.DEFAULT_MODEL;

    console.log("GPT配置验证通过", { baseUrl, model, hasApiKey: !!apiKey });

    // 执行翻译流程
    console.log("开始下载字幕文件...");
    const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

    console.log("解析SRT字幕文件...");
    const subtitleEntries = parseSRT(srtContent);
    if (subtitleEntries.length === 0) {
      throw new Error("字幕文件为空或格式错误");
    }
    console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

    // 执行纯并发翻译
    console.log("开始纯并发翻译...");
    const translatedEntries = await translateSubtitlesBatchOptimized(
      subtitleEntries,
      apiKey,
      baseUrl,
      model,
      actualSourceLanguage,
      normalizedTargetLanguage
    );
    console.log(`翻译完成，共处理 ${translatedEntries.length} 条字幕`);

    const result = await processSubtitles(taskId, translatedEntries, normalizedTargetLanguage, tasksCollection);

    const processingTime = (Date.now() - startTime) / 1000;
    console.log(`字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

    return createSuccessResponse("字幕翻译成功", {
      taskId,
      status: "completed",
      translatedCount: result.translatedCount,
      subtitleOssUrl: result.subtitleOssUrl,
      processingTime,
      sourceLanguage: actualSourceLanguage,
      targetLanguage: normalizedTargetLanguage,
    });
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
    });
  }
};
