#!/usr/bin/env node

/**
 * 开发启动脚本
 * 提供便捷的开发环境启动方式
 */

const { spawn } = require('child_process')
const path = require('path')

// 获取命令行参数
const args = process.argv.slice(2)
const platform = args[0] || 'mp-weixin'

// 支持的平台
const platforms = {
  'mp-weixin': '微信小程序',
  'h5': 'H5',
  'mp-alipay': '支付宝小程序',
  'mp-baidu': '百度小程序',
  'mp-toutiao': '字节跳动小程序',
  'mp-qq': 'QQ小程序'
}

console.log(`🚀 启动 ${platforms[platform] || platform} 开发环境...`)

// 构建命令
const command = process.platform === 'win32' ? 'npm.cmd' : 'npm'
const scriptArgs = ['run', `dev:${platform}`]

// 启动开发服务器
const child = spawn(command, scriptArgs, {
  stdio: 'inherit',
  cwd: path.resolve(__dirname, '..')
})

child.on('error', (error) => {
  console.error('❌ 启动失败:', error.message)
  process.exit(1)
})

child.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ 进程退出，退出码: ${code}`)
    process.exit(code)
  }
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭开发服务器...')
  child.kill('SIGINT')
})

process.on('SIGTERM', () => {
  child.kill('SIGTERM')
})
