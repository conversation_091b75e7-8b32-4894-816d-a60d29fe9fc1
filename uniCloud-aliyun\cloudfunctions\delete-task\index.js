"use strict";

/**
 * 删除任务云函数
 * 
 * 功能说明：
 * 1. 验证用户权限，确保只能删除自己的任务
 * 2. 检查任务状态，避免删除正在处理的任务
 * 3. 清理相关资源（可选，根据业务需求）
 * 4. 软删除或硬删除任务记录
 * 
 * @param {Object} event 
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（必需）
 * @param {boolean} event.forceDelete - 是否强制删除（可选，默认false）
 * @returns {Object} 删除结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid, forceDelete = false } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log('delete-task 云函数被调用，参数：', { 
      taskId, 
      hasOpenid: !!openid, 
      forceDelete,
      clientIP: CLIENTIP 
    });

    // ==================== 参数验证 ====================
    if (!taskId) {
      console.error('参数验证失败：缺少taskId参数');
      return {
        code: 400,
        message: '缺少必要参数：taskId'
      };
    }

    if (!openid) {
      console.error('参数验证失败：缺少openid参数');
      return {
        code: 400,
        message: '缺少必要参数：openid'
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== 'string' || taskId.length === 0) {
      console.error('参数验证失败：taskId格式不正确');
      return {
        code: 400,
        message: 'taskId格式不正确'
      };
    }

    // ==================== 数据库连接 ====================
    const db = uniCloud.database();
    const tasksCollection = db.collection('tasks');
    const usersCollection = db.collection('users');

    // ==================== 用户权限验证 ====================
    console.log('开始验证用户权限...');
    
    // 验证用户是否存在
    const userResult = await usersCollection.where({
      openid: openid
    }).field({
      _id: true,
      status: true
    }).limit(1).get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error('用户验证失败：用户不存在', openid.substring(0, 8) + '***');
      return {
        code: 401,
        message: '用户不存在，请先登录'
      };
    }

    const userData = userResult.data[0];
    const userId = userData._id;

    // 检查用户状态
    if (userData.status === 'banned') {
      console.error('用户验证失败：用户已被封禁', userId);
      return {
        code: 403,
        message: '用户已被封禁，无法执行此操作'
      };
    }

    console.log('用户验证通过，userId：', userId);

    // ==================== 任务权限验证 ====================
    console.log('开始验证任务权限...');
    
    // 查询任务信息，验证任务是否存在且属于当前用户
    const taskResult = await tasksCollection.where({
      _id: taskId,
      userId: userId
    }).field({
      _id: true,
      status: true,
      fileName: true,
      ossUrl: true,
      audioOssUrl: true,
      subtitleOssUrl: true,
      finalVideoUrl: true,
      createTime: true
    }).limit(1).get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error('任务验证失败：任务不存在或无权限访问', taskId);
      return {
        code: 404,
        message: '任务不存在或无权限访问'
      };
    }

    const taskData = taskResult.data[0];
    console.log('任务验证通过，任务状态：', taskData.status);

    // ==================== 业务逻辑验证 ====================

    // 允许删除任何状态的任务，移除处理中任务的删除限制
    console.log('准备删除任务，当前状态：', taskData.status);

    // ==================== 执行删除操作 ====================
    console.log('开始执行删除操作...');
    
    try {
      // 执行软删除（推荐）- 将状态标记为已删除，保留数据用于审计
      const deleteResult = await tasksCollection.doc(taskId).update({
        status: 'deleted',
        deletedAt: new Date(),
        updateTime: new Date()
      });

      // 如果需要硬删除，可以使用以下代码替换上面的软删除
      // const deleteResult = await tasksCollection.doc(taskId).remove();

      console.log('任务删除成功，更新记录数：', deleteResult.updated);

      // ==================== 返回成功结果 ====================
      return {
        code: 200,
        message: '任务删除成功',
        data: {
          taskId: taskId,
          fileName: taskData.videoInfo?.fileName,
          deletedAt: new Date(),
          operation: 'soft_delete' // 标识删除类型
        }
      };

    } catch (deleteError) {
      console.error('执行删除操作失败：', deleteError);
      throw deleteError;
    }

  } catch (error) {
    console.error('delete-task 云函数执行错误：', error);
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};
