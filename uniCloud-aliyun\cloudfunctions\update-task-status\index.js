// uniCloud云函数：更新任务状态
'use strict';

/**
 * 更新任务状态
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.status - 新状态（必需）
 * @param {string} event.openid - 用户openid（用于权限验证，必需）
 * @param {string} event.ossUrl - OSS文件地址（可选）
 * @param {Object} event.fileInfo - 文件信息对象（可选）
 * @param {string} event.fileInfo.fileName - 文件名
 * @param {number} event.fileInfo.fileSize - 文件大小（字节）
 * @param {number} event.fileInfo.duration - 视频时长（秒）
 * @param {number} event.fileInfo.videoWidth - 视频宽度（像素）
 * @param {number} event.fileInfo.videoHeight - 视频高度（像素）
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, status, openid, ossUrl, fileInfo } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log('update-task-status 云函数被调用，参数：', { taskId, status, hasOpenid: !!openid, hasOssUrl: !!ossUrl, hasFileInfo: !!fileInfo });

    // 参数验证
    if (!taskId) {
      console.error('参数验证失败：缺少taskId参数');
      return {
        code: 400,
        message: '缺少必要参数：taskId'
      };
    }

    if (!status) {
      console.error('参数验证失败：缺少status参数');
      return {
        code: 400,
        message: '缺少必要参数：status'
      };
    }

    if (!openid) {
      console.error('参数验证失败：缺少openid参数');
      return {
        code: 400,
        message: '缺少必要参数：openid'
      };
    }

    // 验证状态值是否有效
    const validStatuses = ['uploading', 'extracting_audio', 'recognizing', 'translating', 'merging', 'completed', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      console.error('参数验证失败：status值无效', status);
      return {
        code: 400,
        message: '无效的状态值：' + status
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection('tasks');
    const usersCollection = db.collection('users');

    // 验证用户是否存在
    const userResult = await usersCollection.where({
      openid: openid
    }).field({
      _id: true
    }).get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error('用户验证失败：用户不存在', openid);
      return {
        code: 401,
        message: '用户不存在，请先登录'
      };
    }

    const userId = userResult.data[0]._id;
    console.log('用户验证通过，userId：', userId);

    // 验证任务是否存在且属于当前用户，同时获取现有的videoInfo数据
    const taskResult = await tasksCollection.where({
      _id: taskId,
      userId: userId
    }).field({
      _id: true,
      status: true,
      videoInfo: true  // 获取现有的videoInfo数据，用于合并
    }).get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error('任务验证失败：任务不存在或无权限访问', taskId);
      return {
        code: 404,
        message: '任务不存在或无权限访问'
      };
    }

    const currentTask = taskResult.data[0];
    console.log('任务验证通过，当前状态：', currentTask.status);

    // 构建更新数据
    const updateData = {
      status: status,
      updateTime: new Date()
    };

    // 如果提供了ossUrl，则一并更新
    if (ossUrl) {
      updateData.ossUrl = ossUrl;
    }

    // 如果提供了文件信息，则更新到videoInfo字段中
    if (fileInfo && typeof fileInfo === 'object') {
      console.log('更新文件信息到videoInfo字段:', fileInfo);

      // 获取现有的videoInfo数据，进行智能合并
      const existingVideoInfo = currentTask.videoInfo || {};

      // 构建videoInfo对象，保留现有数据并合并新数据
      // 优先使用新提供的数据，但保留现有的重要信息（如MPS分析结果）
      updateData.videoInfo = {
        // 基本文件信息（优先使用新数据）
        fileName: fileInfo.fileName || existingVideoInfo.fileName || '',
        fileSize: fileInfo.fileSize || existingVideoInfo.fileSize || 0,
        duration: fileInfo.duration || existingVideoInfo.duration || 0,
        videoWidth: fileInfo.videoWidth || existingVideoInfo.videoWidth || 0,
        videoHeight: fileInfo.videoHeight || existingVideoInfo.videoHeight || 0,

        // 保留现有的高级信息（MPS分析结果等）
        frameRate: existingVideoInfo.frameRate,
        bitRate: existingVideoInfo.bitRate,
        format: existingVideoInfo.format,
        codecName: existingVideoInfo.codecName,
        audioCodec: existingVideoInfo.audioCodec,
        sampleRate: existingVideoInfo.sampleRate,
        channels: existingVideoInfo.channels,
        mpsJobId: existingVideoInfo.mpsJobId,
        analysisTime: existingVideoInfo.analysisTime,
        platform: existingVideoInfo.platform,
        author: existingVideoInfo.author,
        title: existingVideoInfo.title,
        cover: existingVideoInfo.cover,
        originalUrl: existingVideoInfo.originalUrl,
        sourceLanguage: existingVideoInfo.sourceLanguage,
        targetLanguage: existingVideoInfo.targetLanguage,

        // 更新时间
        updateTime: new Date()
      };

      // 为了兼容旧版本，同时更新根级别字段
      updateData.fileName = fileInfo.fileName || existingVideoInfo.fileName || '';
      updateData.fileSize = fileInfo.fileSize || existingVideoInfo.fileSize || 0;
      updateData.duration = fileInfo.duration || existingVideoInfo.duration || 0;

      if (fileInfo.videoWidth || existingVideoInfo.videoWidth) {
        updateData.videoWidth = fileInfo.videoWidth || existingVideoInfo.videoWidth;
      }
      if (fileInfo.videoHeight || existingVideoInfo.videoHeight) {
        updateData.videoHeight = fileInfo.videoHeight || existingVideoInfo.videoHeight;
      }
    }

    // 更新任务状态
    const updateResult = await tasksCollection.doc(taskId).update(updateData);

    console.log('任务状态更新成功，更新记录数：', updateResult.updated);

    return {
      code: 200,
      message: '任务状态更新成功',
      data: {
        taskId: taskId,
        oldStatus: currentTask.status,
        newStatus: status,
        updated: updateResult.updated,
        fileInfoUpdated: !!fileInfo
      }
    };

  } catch (error) {
    console.error('update-task-status 云函数执行错误：', error);
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};
