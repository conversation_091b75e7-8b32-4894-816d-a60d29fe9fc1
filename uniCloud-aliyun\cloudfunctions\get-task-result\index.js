// uniCloud云函数：获取任务处理结果
"use strict";

/**
 * 获取视频处理任务的最终结果
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（用于权限验证，可选）
 * @returns {Object} 任务结果信息
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid } = event;

    console.log("get-task-result 云函数被调用，参数：", { taskId, hasOpenid: !!openid });

    // 参数验证
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        code: 400,
        message: "taskId格式不正确",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    console.log("准备查询任务结果，taskId：", taskId);

    // 查询任务信息
    let taskQuery = tasksCollection.where({
      _id: taskId,
    });

    // 如果提供了openid，添加用户权限验证
    if (openid) {
      // 首先查询用户ID
      const usersCollection = db.collection("users");
      const userResult = await usersCollection
        .where({
          openid: openid,
        })
        .field({
          _id: true,
        })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid);
        return {
          code: 403,
          message: "用户不存在",
        };
      }

      const userId = userResult.data[0]._id;
      console.log("用户验证通过，userId：", userId);

      // 添加用户权限过滤
      taskQuery = taskQuery.where({
        userId: userId,
      });
    }

    // 查询任务完整信息
    const taskResult = await taskQuery
      .field({
        _id: true,
        status: true,
        finalVideoUrl: true,
        subtitleOssUrl: true,
        errorMessage: true,
        fileName: true,
        fileSize: true,
        duration: true,
        createTime: true,
        updateTime: true,
      })
      .limit(1)
      .get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务查询失败：任务不存在或无权限访问", taskId);
      return {
        code: 404,
        message: "任务不存在或无权限访问",
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务查询成功，状态：", taskData.status);

    // 检查任务是否已完成
    if (taskData.status !== "completed") {
      return {
        code: 400,
        message: "任务尚未完成",
        data: {
          status: taskData.status,
          errorMessage: taskData.errorMessage,
        },
      };
    }

    // 构建返回数据
    const responseData = {
      taskId: taskData._id,
      status: taskData.status,
      videoUrl: taskData.finalVideoUrl || "",
      subtitleUrl: taskData.subtitleOssUrl || "",
      subtitles: [], // 暂时返回空数组，后续可以解析SRT文件
      fileInfo: {
        fileName: taskData.videoInfo?.fileName || "",
        fileSize: taskData.videoInfo?.fileSize || 0,
        duration: taskData.videoInfo?.duration || 0,
      },
      createTime: taskData.createTime,
      updateTime: taskData.updateTime,
    };

    // 如果有字幕文件，尝试解析并返回字幕内容
    if (taskData.subtitleOssUrl) {
      try {
        const subtitleContent = await downloadSubtitleFromOss(taskData.subtitleOssUrl);
        const parsedSubtitles = parseSubtitleFile(subtitleContent, taskData.subtitleOssUrl);
        responseData.subtitles = parsedSubtitles;
      } catch (error) {
        console.warn("解析字幕文件失败：", error);
        // 不影响主要功能，继续返回其他数据
      }
    }

    return {
      code: 200,
      message: "查询成功",
      data: responseData,
    };
  } catch (error) {
    console.error("get-task-result 云函数执行错误：", error);

    return {
      code: 500,
      message: "查询任务结果失败: " + error.message,
    };
  }
};

/**
 * 从OSS下载字幕文件内容（支持SRT和ASS格式）
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} 字幕文件内容
 */
async function downloadSubtitleFromOss(ossUrl) {
  const https = require("https");
  const http = require("http");
  const url = require("url");

  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(ossUrl);
    const client = parsedUrl.protocol === "https:" ? https : http;

    const req = client.get(ossUrl, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          resolve(data);
        } else {
          reject(new Error(`下载SRT文件失败，状态码: ${res.statusCode}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`下载SRT文件失败: ${error.message}`));
    });
  });
}

/**
 * 根据文件URL判断格式并解析字幕文件
 * @param {string} subtitleContent - 字幕文件内容
 * @param {string} fileUrl - 文件URL（用于判断格式）
 * @returns {Array} 解析后的字幕数组
 */
function parseSubtitleFile(subtitleContent, fileUrl) {
  // 根据文件扩展名判断格式
  const isAssFormat = fileUrl.toLowerCase().includes('.ass');

  if (isAssFormat) {
    return parseASS(subtitleContent);
  } else {
    return parseSRT(subtitleContent);
  }
}

/**
 * 解析ASS字幕文件
 * @param {string} assContent - ASS文件内容
 * @returns {Array} 解析后的字幕数组
 */
function parseASS(assContent) {
  const subtitles = [];
  const lines = assContent.split('\n');

  // 查找Events部分
  let inEventsSection = false;

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (trimmedLine === '[Events]') {
      inEventsSection = true;
      continue;
    }

    if (trimmedLine.startsWith('[') && trimmedLine.endsWith(']') && trimmedLine !== '[Events]') {
      inEventsSection = false;
      continue;
    }

    if (inEventsSection && trimmedLine.startsWith('Dialogue:')) {
      const dialogueParts = trimmedLine.substring(9).split(',');

      if (dialogueParts.length >= 10) {
        const layer = dialogueParts[0];
        const startTime = convertASSTimeToSRT(dialogueParts[1]);
        const endTime = convertASSTimeToSRT(dialogueParts[2]);
        const text = dialogueParts.slice(9).join(',').trim();

        if (startTime && endTime && text) {
          subtitles.push({
            index: subtitles.length + 1,
            startTime: startTime,
            endTime: endTime,
            text: text.replace(/\\N/g, '\n'), // 转换ASS换行符
          });
        }
      }
    }
  }

  return subtitles;
}

/**
 * 将ASS时间格式转换为SRT时间格式
 * @param {string} assTime - ASS时间格式 (H:MM:SS.cc)
 * @returns {string} SRT时间格式 (HH:MM:SS,mmm)
 */
function convertASSTimeToSRT(assTime) {
  try {
    const timeStr = assTime.trim();
    const [time, centiseconds] = timeStr.split('.');
    const [hours, minutes, seconds] = time.split(':');

    // 转换厘秒为毫秒
    const milliseconds = Math.round(parseInt(centiseconds || '0') * 10);

    // 格式化为SRT格式
    const formattedHours = hours.padStart(2, '0');
    const formattedMilliseconds = milliseconds.toString().padStart(3, '0');

    return `${formattedHours}:${minutes}:${seconds},${formattedMilliseconds}`;
  } catch (error) {
    console.warn('时间格式转换失败:', assTime, error);
    return null;
  }
}

/**
 * 解析SRT字幕文件
 * @param {string} srtContent - SRT文件内容
 * @returns {Array} 解析后的字幕数组
 */
function parseSRT(srtContent) {
  const subtitles = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  blocks.forEach((block) => {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      // 解析时间范围
      const timeMatch = timeRange.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
      if (timeMatch) {
        subtitles.push({
          index,
          startTime: timeMatch[1],
          endTime: timeMatch[2],
          text: text.trim(),
        });
      }
    }
  });

  return subtitles;
}
