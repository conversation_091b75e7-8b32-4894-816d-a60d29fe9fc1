{"bsonType": "object", "required": ["operation", "operationTime"], "properties": {"_id": {"description": "日志ID，系统自动生成"}, "operation": {"bsonType": "string", "description": "操作类型", "title": "操作类型", "enum": ["cleanup_old_data", "manual_delete", "batch_delete"]}, "operationTime": {"bsonType": "timestamp", "description": "操作执行时间", "title": "操作时间"}, "details": {"bsonType": "object", "description": "操作详细信息", "title": "操作详情", "properties": {"cutoffDate": {"bsonType": "timestamp", "description": "清理截止日期", "title": "截止日期"}, "result": {"bsonType": "object", "description": "操作结果统计", "title": "操作结果"}, "error": {"bsonType": "string", "description": "错误信息", "title": "错误信息"}}}, "clientIP": {"bsonType": "string", "description": "客户端IP地址", "title": "客户端IP"}, "createTime": {"bsonType": "timestamp", "description": "日志创建时间", "title": "创建时间"}}}