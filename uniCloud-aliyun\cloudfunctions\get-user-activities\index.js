// uniCloud云函数：获取用户活动数据
"use strict";

/**
 * 获取用户活动数据和统计信息
 *
 * @param {Object} event
 * @param {string} event.openid - 用户openid（必需）
 * @param {string} event.timeRange - 时间范围（可选：'7d', '30d', '90d', 'all'，默认'30d'）
 * @param {boolean} event.includeStats - 是否包含统计信息（可选，默认true）
 * @returns {Object} 用户活动数据和统计信息
 */
exports.main = async (event, context) => {
  try {
    const { openid, timeRange = '30d', includeStats = true } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("get-user-activities 云函数被调用，参数：", { 
      openid: openid ? openid.substring(0, 8) + '***' : null, 
      timeRange, 
      includeStats 
    });

    // 参数验证
    if (!openid) {
      console.error("参数验证失败：缺少openid参数");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "缺少必要参数：openid",
        code: 400
      };
    }

    // 验证时间范围参数
    const validTimeRanges = ['7d', '30d', '90d', 'all'];
    if (!validTimeRanges.includes(timeRange)) {
      console.error("参数验证失败：无效的时间范围", timeRange);
      return {
        errCode: "INVALID_PARAM",
        errMsg: "无效的时间范围参数",
        code: 400
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const usersCollection = db.collection("users");
    const tasksCollection = db.collection("tasks");

    console.log("开始验证用户身份...");

    // 验证用户是否存在
    const userResult = await usersCollection
      .where({
        openid: openid,
      })
      .field({
        _id: true,
        status: true,
        createTime: true,
        lastLoginTime: true,
        lastActiveTime: true
      })
      .limit(1)
      .get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error("用户验证失败：用户不存在", openid.substring(0, 8) + '***');
      return {
        errCode: "USER_NOT_FOUND",
        errMsg: "用户不存在",
        code: 404
      };
    }

    const userData = userResult.data[0];
    const userId = userData._id;

    // 检查用户状态
    if (userData.status === 'banned') {
      console.error("用户验证失败：用户已被封禁", userId);
      return {
        errCode: "USER_BANNED",
        errMsg: "用户已被封禁",
        code: 403
      };
    }

    console.log("用户验证通过，userId：", userId);

    // 计算查询时间范围
    let queryStartDate = null;
    if (timeRange !== 'all') {
      queryStartDate = new Date();
      const days = parseInt(timeRange.replace('d', ''));
      queryStartDate.setDate(queryStartDate.getDate() - days);
    }

    console.log("查询时间范围：", queryStartDate ? queryStartDate.toISOString() : '全部时间', "至今");

    // 构建查询条件
    let queryCondition = { userId: userId };
    if (queryStartDate) {
      queryCondition.createTime = db.command.gte(queryStartDate);
    }

    // 查询用户任务数据
    console.log("开始查询用户活动数据...");
    const tasksResult = await tasksCollection
      .where(queryCondition)
      .field({
        _id: true,
        status: true,
        createTime: true,
        updateTime: true,
        fileSize: true,
        duration: true
      })
      .orderBy('createTime', 'desc')
      .get();

    const tasks = tasksResult.data || [];
    console.log(`查询完成，找到 ${tasks.length} 条任务记录`);

    // 构建返回数据
    const responseData = {
      userId: userId,
      timeRange: timeRange,
      queryPeriod: {
        startDate: queryStartDate ? queryStartDate.toISOString() : null,
        endDate: new Date().toISOString()
      }
    };

    // 如果需要包含统计信息
    if (includeStats) {
      responseData.statistics = calculateUserStatistics(tasks, userData);
    }

    // 活动时间线（最近的活动记录）
    responseData.recentActivities = generateActivityTimeline(tasks.slice(0, 20));

    // 任务状态分布
    responseData.taskStatusDistribution = calculateStatusDistribution(tasks);

    // 使用情况趋势（按天统计）
    if (queryStartDate) {
      responseData.usageTrend = calculateUsageTrend(tasks, queryStartDate);
    }

    // 更新用户最后活跃时间
    try {
      await usersCollection
        .where({ _id: userId })
        .update({
          lastActiveTime: new Date(),
          lastActiveIP: CLIENTIP,
          lastActiveUA: CLIENTUA
        });
    } catch (updateError) {
      console.warn("更新用户活跃时间失败：", updateError.message);
    }

    return {
      errCode: 0,
      code: 200,
      errMsg: "查询成功",
      data: responseData
    };

  } catch (error) {
    console.error("get-user-activities 云函数执行错误：", error);

    return {
      errCode: "GET_USER_ACTIVITIES_FAILED",
      code: 500,
      errMsg: "获取用户活动数据失败: " + error.message,
    };
  }
};

/**
 * 计算用户统计信息
 * @param {Array} tasks 任务列表
 * @param {Object} userData 用户数据
 * @returns {Object} 统计信息
 */
function calculateUserStatistics(tasks, userData) {
  const stats = {
    totalTasks: tasks.length,
    completedTasks: 0,
    failedTasks: 0,
    processingTasks: 0,
    totalProcessingTime: 0,
    totalFileSize: 0,
    totalDuration: 0,
    averageProcessingTime: 0,
    successRate: 0,
    memberSince: userData.createTime,
    lastActiveTime: userData.lastActiveTime
  };

  let totalProcessingTimeMs = 0;
  let completedTasksWithTime = 0;

  tasks.forEach(task => {
    // 统计任务状态
    switch (task.status) {
      case 'completed':
        stats.completedTasks++;
        break;
      case 'failed':
        stats.failedTasks++;
        break;
      case 'uploading':
      case 'extracting_audio':
      case 'recognizing':
      case 'translating':
      case 'merging':
        stats.processingTasks++;
        break;
    }

    // 累计文件大小和时长
    if (task.fileSize) {
      stats.totalFileSize += task.fileSize;
    }
    if (task.duration) {
      stats.totalDuration += task.duration;
    }

    // 计算处理时间（仅对已完成的任务）
    if (task.status === 'completed' && task.createTime && task.updateTime) {
      const processingTime = new Date(task.updateTime) - new Date(task.createTime);
      totalProcessingTimeMs += processingTime;
      completedTasksWithTime++;
    }
  });

  // 计算平均处理时间（秒）
  if (completedTasksWithTime > 0) {
    stats.averageProcessingTime = Math.round(totalProcessingTimeMs / completedTasksWithTime / 1000);
  }

  // 计算成功率
  if (stats.totalTasks > 0) {
    stats.successRate = Math.round((stats.completedTasks / stats.totalTasks) * 100);
  }

  return stats;
}

/**
 * 生成活动时间线
 * @param {Array} tasks 最近的任务列表
 * @returns {Array} 活动时间线
 */
function generateActivityTimeline(tasks) {
  return tasks.map(task => ({
    taskId: task._id,
    action: getActionByStatus(task.status),
    status: task.status,
    timestamp: task.updateTime || task.createTime,
    description: generateActivityDescription(task)
  }));
}

/**
 * 计算任务状态分布
 * @param {Array} tasks 任务列表
 * @returns {Object} 状态分布
 */
function calculateStatusDistribution(tasks) {
  const distribution = {
    completed: 0,
    failed: 0,
    processing: 0,
    pending: 0
  };

  tasks.forEach(task => {
    switch (task.status) {
      case 'completed':
        distribution.completed++;
        break;
      case 'failed':
        distribution.failed++;
        break;
      case 'uploading':
      case 'extracting_audio':
      case 'recognizing':
      case 'translating':
      case 'merging':
        distribution.processing++;
        break;
      default:
        distribution.pending++;
    }
  });

  return distribution;
}

/**
 * 计算使用趋势
 * @param {Array} tasks 任务列表
 * @param {Date} startDate 开始日期
 * @returns {Array} 使用趋势数据
 */
function calculateUsageTrend(tasks, startDate) {
  const trend = [];
  const now = new Date();
  const dayMs = 24 * 60 * 60 * 1000;

  // 生成日期范围
  for (let date = new Date(startDate); date <= now; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().split('T')[0];
    const dayTasks = tasks.filter(task => {
      const taskDate = new Date(task.createTime).toISOString().split('T')[0];
      return taskDate === dateStr;
    });

    trend.push({
      date: dateStr,
      taskCount: dayTasks.length,
      completedCount: dayTasks.filter(t => t.status === 'completed').length,
      failedCount: dayTasks.filter(t => t.status === 'failed').length
    });
  }

  return trend;
}

/**
 * 根据状态获取动作描述
 * @param {string} status 任务状态
 * @returns {string} 动作描述
 */
function getActionByStatus(status) {
  const actionMap = {
    uploading: '上传视频',
    extracting_audio: '提取音频',
    recognizing: '语音识别',
    translating: '生成字幕',
    merging: '合成视频',
    completed: '任务完成',
    failed: '任务失败'
  };
  return actionMap[status] || '未知操作';
}

/**
 * 生成活动描述
 * @param {Object} task 任务对象
 * @returns {string} 活动描述
 */
function generateActivityDescription(task) {
  const action = getActionByStatus(task.status);
  const time = new Date(task.updateTime || task.createTime).toLocaleString('zh-CN');
  return `${time} - ${action}`;
}
