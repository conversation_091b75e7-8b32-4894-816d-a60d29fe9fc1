// uniCloud云函数：获取微信用户openid
"use strict";

// 引入uni-config-center配置中心
const createConfig = require("uni-config-center");

/**
 * 通过微信登录code获取用户openid
 *
 * @param {Object} event
 * @param {string} event.code - 微信登录返回的code
 * @param {string} event.deviceId - 设备唯一标识
 * @returns {Object} 包含openid和用户信息的对象
 */
exports.main = async (event, context) => {
  try {
    const { code, deviceId } = event;
    const { CLIENTUA, CLIENTIP } = context;

    // 参数验证
    if (!code) {
      return {
        errCode: "INVALID_PARAM",
        errMsg: "code参数不能为空",
      };
    }

    if (!deviceId) {
      return {
        errCode: "INVALID_PARAM",
        errMsg: "deviceId参数不能为空",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    console.log("数据库连接成功");

    // 获取微信小程序配置
    const wechatConfig = createConfig({
      pluginId: "wechat-mp",
    });

    const appid = wechatConfig.config("appid");
    const secret = wechatConfig.config("secret");

    // 验证微信配置
    if (!appid || !secret) {
      console.error("微信小程序配置检查失败：", {
        hasAppid: !!appid,
        hasSecret: !!secret,
      });
      return {
        errCode: "CONFIG_ERROR",
        errMsg: "微信小程序配置错误，请联系管理员",
      };
    }

    console.log("微信小程序配置检查通过，appid:", appid);

    // 调用微信接口获取openid
    const wxResult = await uniCloud.httpclient.request(
      "https://api.weixin.qq.com/sns/jscode2session",
      {
        method: "GET",
        data: {
          appid: appid,
          secret: secret,
          js_code: code,
          grant_type: "authorization_code",
        },
        dataType: "json",
      }
    );

    console.log("微信接口返回:", wxResult);

    if (wxResult.status !== 200) {
      throw new Error("调用微信接口失败");
    }

    const wxData = wxResult.data;

    if (wxData.errcode) {
      let errorMsg = "获取openid失败";
      switch (wxData.errcode) {
        case 40013:
          errorMsg = "无效的AppID";
          break;
        case 40014:
          errorMsg = "无效的AppSecret";
          break;
        case 40029:
          errorMsg = "无效的code";
          break;
        case 45011:
          errorMsg = "API调用太频繁";
          break;
        default:
          errorMsg = `微信接口错误: ${wxData.errmsg}`;
      }

      return {
        errCode: "WX_API_ERROR",
        errMsg: errorMsg,
        wxError: wxData,
      };
    }

    const { openid, session_key, unionid } = wxData;

    if (!openid) {
      return {
        errCode: "NO_OPENID",
        errMsg: "未获取到openid",
      };
    }

    // 查询或创建用户记录
    const userCollection = db.collection("users");
    console.log("准备查询用户集合");

    // 先查询用户是否已存在
    let existingUser;
    try {
      existingUser = await userCollection
        .where({
          openid: openid,
        })
        .get();
      console.log("用户查询结果:", existingUser);
    } catch (dbError) {
      console.error("数据库查询失败:", dbError);
      throw new Error(`数据库查询失败: ${dbError.message}`);
    }

    let userData = {
      openid: openid,
      deviceId: deviceId,
      sessionKey: session_key,
      unionid: unionid || null,
      lastLoginTime: new Date(),
      lastLoginIP: CLIENTIP,
      lastLoginUA: CLIENTUA,
    };

    let userId = null;

    if (existingUser.data && existingUser.data.length > 0) {
      // 用户已存在，更新登录信息
      userId = existingUser.data[0]._id;
      await userCollection.doc(userId).update({
        deviceId: deviceId,
        sessionKey: session_key,
        lastLoginTime: new Date(),
        lastLoginIP: CLIENTIP,
        lastLoginUA: CLIENTUA,
      });

      userData = { ...existingUser.data[0], ...userData };
    } else {
      // 新用户，创建记录
      userData.createTime = new Date();
      userData.nickname = null; // 将在前端获取用户信息后更新
      userData.avatar = null;

      const createResult = await userCollection.add(userData);
      userId = createResult.id;
      userData._id = userId;
    }

    console.log("用户信息处理完成:", { userId, openid });

    return {
      errCode: 0,
      errMsg: "获取openid成功",
      code: 200,
      data: {
        openid: openid,
        userId: userId,
        sessionKey: session_key,
        unionid: unionid || null,
        isNewUser: existingUser.data.length === 0,
      },
    };
  } catch (error) {
    console.error("获取微信openid失败:", error);
    return {
      errCode: "GET_OPENID_FAILED",
      code: 500,
      errMsg: "获取openid失败: " + error.message,
    };
  }
};
